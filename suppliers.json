{"GT408": {"code": "GT408", "name": "供应商GT408", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.966596", "last_used": "", "usage_count": 0, "is_active": true}, "GT251": {"code": "GT251", "name": "供应商GT251", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.966596", "last_used": "", "usage_count": 0, "is_active": true}, "GT155": {"code": "GT155", "name": "供应商GT155", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.967596", "last_used": "", "usage_count": 0, "is_active": true}, "GT253": {"code": "GT253", "name": "供应商GT253", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.967596", "last_used": "", "usage_count": 0, "is_active": true}, "DSD258": {"code": "DSD258", "name": "供应商DSD258", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.967596", "last_used": "", "usage_count": 0, "is_active": true}, "DSD129": {"code": "DSD129", "name": "供应商DSD129", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.968597", "last_used": "", "usage_count": 0, "is_active": true}, "DSD106": {"code": "DSD106", "name": "供应商DSD106", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.968597", "last_used": "", "usage_count": 0, "is_active": true}, "GD340": {"code": "GD340", "name": "供应商GD340", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.969597", "last_used": "", "usage_count": 0, "is_active": true}, "GT158": {"code": "GT158", "name": "供应商GT158", "description": "默认供应商", "created_time": "2025-06-05T16:30:18.969597", "last_used": "", "usage_count": 0, "is_active": true}}