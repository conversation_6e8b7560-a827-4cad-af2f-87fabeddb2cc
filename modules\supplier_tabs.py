#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商多标签页组件
"""

import tkinter as tk
from tkinter import ttk, messagebox
from typing import Dict, List, Optional, Callable
from modules.supplier_manager import SupplierGroup


class SupplierTab:
    """单个供应商标签页"""
    
    def __init__(self, parent, supplier_code: str, supplier_name: str):
        self.parent = parent
        self.supplier_code = supplier_code
        self.supplier_name = supplier_name
        self.frame = None
        self.tree = None
        self.detail_frame = None
        self.stats_frame = None
        self.items = []
        
        self.create_tab()
    
    def create_tab(self):
        """创建标签页内容"""
        self.frame = ttk.Frame(self.parent)
        
        # 创建主要区域
        main_paned = ttk.PanedWindow(self.frame, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 左侧：商品列表
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=3)
        
        # 商品列表标题和工具栏
        list_header = ttk.Frame(left_frame)
        list_header.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(list_header, text=f"商品列表 - {self.supplier_name}", 
                 font=("Arial", 10, "bold")).pack(side=tk.LEFT)
        
        # 工具按钮
        btn_frame = ttk.Frame(list_header)
        btn_frame.pack(side=tk.RIGHT)
        
        ttk.Button(btn_frame, text="刷新", command=self.refresh_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="导出", command=self.export_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="清空", command=self.clear_data).pack(side=tk.LEFT)
        
        # 商品列表表格
        tree_frame = ttk.Frame(left_frame)
        tree_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建Treeview
        columns = ("序号", "商品名称", "价格", "数量", "类型", "来源文件")
        self.tree = ttk.Treeview(tree_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        column_widths = {"序号": 50, "商品名称": 200, "价格": 80, "数量": 60, "类型": 80, "来源文件": 150}
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100), minwidth=50)
        
        # 添加滚动条
        tree_scroll_y = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        tree_scroll_x = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=tree_scroll_y.set, xscrollcommand=tree_scroll_x.set)
        
        # 布局
        self.tree.grid(row=0, column=0, sticky="nsew")
        tree_scroll_y.grid(row=0, column=1, sticky="ns")
        tree_scroll_x.grid(row=1, column=0, sticky="ew")
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定选择事件
        self.tree.bind("<<TreeviewSelect>>", self.on_item_select)
        
        # 右侧：详情和统计
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)
        
        # 统计信息
        self.create_stats_section(right_frame)
        
        # 商品详情
        self.create_detail_section(right_frame)
    
    def create_stats_section(self, parent):
        """创建统计信息区域"""
        stats_label_frame = ttk.LabelFrame(parent, text="统计信息")
        stats_label_frame.pack(fill=tk.X, padx=5, pady=(0, 10))
        
        self.stats_frame = ttk.Frame(stats_label_frame)
        self.stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 统计标签
        self.stats_labels = {}
        stats_items = [
            ("total_items", "商品总数"),
            ("total_files", "文件数量"),
            ("total_amount", "总金额"),
            ("avg_price", "平均价格")
        ]
        
        for i, (key, label) in enumerate(stats_items):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(self.stats_frame, text=f"{label}:").grid(row=row, column=col, sticky="w", padx=(0, 5))
            self.stats_labels[key] = ttk.Label(self.stats_frame, text="0", font=("Arial", 9, "bold"))
            self.stats_labels[key].grid(row=row, column=col+1, sticky="w", padx=(0, 20))
    
    def create_detail_section(self, parent):
        """创建商品详情区域"""
        detail_label_frame = ttk.LabelFrame(parent, text="商品详情")
        detail_label_frame.pack(fill=tk.BOTH, expand=True, padx=5)
        
        self.detail_frame = ttk.Frame(detail_label_frame)
        self.detail_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 详情显示区域
        self.detail_text = tk.Text(self.detail_frame, height=10, wrap=tk.WORD, 
                                  font=("Consolas", 9))
        detail_scroll = ttk.Scrollbar(self.detail_frame, orient=tk.VERTICAL, 
                                     command=self.detail_text.yview)
        self.detail_text.configure(yscrollcommand=detail_scroll.set)
        
        self.detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始提示
        self.detail_text.insert(tk.END, "请选择商品查看详细信息...")
        self.detail_text.configure(state=tk.DISABLED)
    
    def update_data(self, items: List[Dict]):
        """更新数据"""
        self.items = items
        self.refresh_tree()
        self.update_stats()
    
    def refresh_tree(self):
        """刷新商品列表"""
        # 清空现有数据
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # 添加新数据
        for i, item in enumerate(self.items, 1):
            values = (
                i,
                item.get('name', ''),
                f"¥{item.get('price', 0):.2f}",
                item.get('quantity', 0),
                item.get('type', ''),
                item.get('source_file', '').split('/')[-1] if item.get('source_file') else ''
            )
            self.tree.insert("", tk.END, values=values, tags=(str(i),))
        
        # 设置交替行颜色
        self.tree.tag_configure("odd", background="#f0f0f0")
        for i, item_id in enumerate(self.tree.get_children()):
            if i % 2 == 1:
                self.tree.item(item_id, tags=("odd",))
    
    def update_stats(self):
        """更新统计信息"""
        if not self.items:
            stats = {"total_items": 0, "total_files": 0, "total_amount": 0, "avg_price": 0}
        else:
            total_items = len(self.items)
            total_amount = sum(float(item.get('price', 0)) * int(item.get('quantity', 0)) 
                             for item in self.items)
            avg_price = total_amount / total_items if total_items > 0 else 0
            
            # 统计文件数量
            source_files = set(item.get('source_file', '') for item in self.items)
            total_files = len([f for f in source_files if f])
            
            stats = {
                "total_items": total_items,
                "total_files": total_files,
                "total_amount": total_amount,
                "avg_price": avg_price
            }
        
        # 更新显示
        self.stats_labels["total_items"].config(text=str(stats["total_items"]))
        self.stats_labels["total_files"].config(text=str(stats["total_files"]))
        self.stats_labels["total_amount"].config(text=f"¥{stats['total_amount']:.2f}")
        self.stats_labels["avg_price"].config(text=f"¥{stats['avg_price']:.2f}")
    
    def on_item_select(self, event):
        """处理商品选择事件"""
        selection = self.tree.selection()
        if not selection:
            return
        
        # 获取选中项的索引
        item_id = selection[0]
        values = self.tree.item(item_id, "values")
        if not values:
            return
        
        try:
            index = int(values[0]) - 1
            if 0 <= index < len(self.items):
                self.show_item_detail(self.items[index])
        except (ValueError, IndexError):
            pass
    
    def show_item_detail(self, item: Dict):
        """显示商品详情"""
        self.detail_text.configure(state=tk.NORMAL)
        self.detail_text.delete(1.0, tk.END)
        
        # 格式化详情信息
        detail_info = f"""商品详细信息
{'='*30}

商品名称: {item.get('name', 'N/A')}
价格: ¥{item.get('price', 0):.2f}
数量: {item.get('quantity', 0)}
类型: {item.get('type', 'N/A')}
来源文件: {item.get('source_file', 'N/A')}
添加时间: {item.get('added_time', 'N/A')}

"""
        
        # 如果有额外信息
        extra_info = []
        for key, value in item.items():
            if key not in ['name', 'price', 'quantity', 'type', 'source_file', 'added_time']:
                extra_info.append(f"{key}: {value}")
        
        if extra_info:
            detail_info += "其他信息:\n" + "\n".join(extra_info)
        
        self.detail_text.insert(tk.END, detail_info)
        self.detail_text.configure(state=tk.DISABLED)
    
    def refresh_data(self):
        """刷新数据"""
        self.refresh_tree()
        self.update_stats()
    
    def export_data(self):
        """导出数据"""
        if not self.items:
            messagebox.showwarning("警告", "没有数据可导出")
            return
        
        from tkinter import filedialog
        import csv
        
        filename = filedialog.asksaveasfilename(
            title="导出数据",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)
                    # 写入标题
                    writer.writerow(["序号", "商品名称", "价格", "数量", "类型", "来源文件"])
                    
                    # 写入数据
                    for i, item in enumerate(self.items, 1):
                        writer.writerow([
                            i,
                            item.get('name', ''),
                            item.get('price', 0),
                            item.get('quantity', 0),
                            item.get('type', ''),
                            item.get('source_file', '')
                        ])
                
                messagebox.showinfo("成功", f"数据已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")
    
    def clear_data(self):
        """清空数据"""
        if messagebox.askyesno("确认", f"确定要清空 {self.supplier_name} 的所有数据吗？"):
            self.items.clear()
            self.refresh_data()


class SupplierTabManager:
    """供应商标签页管理器"""
    
    def __init__(self, parent):
        self.parent = parent
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        self.tabs: Dict[str, SupplierTab] = {}
        self.tab_update_callback: Optional[Callable] = None
        
        # 绑定标签页切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)
    
    def add_tab(self, supplier_code: str, supplier_name: str, items: List[Dict] = None) -> SupplierTab:
        """添加标签页"""
        if supplier_code in self.tabs:
            # 更新现有标签页
            tab = self.tabs[supplier_code]
            if items:
                tab.update_data(items)
            return tab
        
        # 创建新标签页
        tab = SupplierTab(self.notebook, supplier_code, supplier_name)
        self.tabs[supplier_code] = tab
        
        # 添加到notebook
        tab_title = f"{supplier_name}"
        if items:
            tab_title += f" ({len(items)})"
            tab.update_data(items)
        
        self.notebook.add(tab.frame, text=tab_title)
        
        return tab
    
    def update_tab(self, supplier_code: str, items: List[Dict]):
        """更新标签页数据"""
        if supplier_code in self.tabs:
            tab = self.tabs[supplier_code]
            tab.update_data(items)
            
            # 更新标签页标题
            tab_index = list(self.tabs.keys()).index(supplier_code)
            new_title = f"{tab.supplier_name} ({len(items)})"
            self.notebook.tab(tab_index, text=new_title)
    
    def remove_tab(self, supplier_code: str):
        """移除标签页"""
        if supplier_code in self.tabs:
            tab = self.tabs[supplier_code]
            tab_index = list(self.tabs.keys()).index(supplier_code)
            self.notebook.forget(tab_index)
            del self.tabs[supplier_code]
    
    def get_current_tab(self) -> Optional[SupplierTab]:
        """获取当前标签页"""
        current_index = self.notebook.index(self.notebook.select())
        supplier_codes = list(self.tabs.keys())
        if 0 <= current_index < len(supplier_codes):
            return self.tabs[supplier_codes[current_index]]
        return None
    
    def clear_all_tabs(self):
        """清空所有标签页"""
        for tab_id in list(self.notebook.tabs()):
            self.notebook.forget(tab_id)
        self.tabs.clear()
    
    def on_tab_changed(self, event):
        """标签页切换事件"""
        if self.tab_update_callback:
            current_tab = self.get_current_tab()
            if current_tab:
                self.tab_update_callback(current_tab.supplier_code)
    
    def set_tab_update_callback(self, callback: Callable):
        """设置标签页更新回调"""
        self.tab_update_callback = callback
    
    def get_all_supplier_codes(self) -> List[str]:
        """获取所有供应商代码"""
        return list(self.tabs.keys())
    
    def get_tab_count(self) -> int:
        """获取标签页数量"""
        return len(self.tabs)
