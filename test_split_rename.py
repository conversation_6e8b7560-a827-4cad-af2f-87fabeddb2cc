#!/usr/bin/env python3
"""
测试分割和重命名功能
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_split_rename_features():
    """测试分割和重命名功能"""
    print("🧪 测试分割和重命名功能")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        # 导入主界面
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI
        
        window = ModernTicketProcessorGUI()
        
        print("✅ 应用程序创建成功")
        
        # 检查分割功能相关方法
        split_methods = [
            'split_selected_image',
            'batch_split_images',
            'check_image_needs_split',
            'split_single_image'
        ]
        
        for method in split_methods:
            if hasattr(window, method):
                print(f"✅ 分割方法 {method} 存在")
            else:
                print(f"❌ 分割方法 {method} 缺失")
                
        # 检查重命名功能相关方法
        rename_methods = [
            'rename_image_by_double_click',
            'rename_selected_thumbnail'
        ]
        
        for method in rename_methods:
            if hasattr(window, method):
                print(f"✅ 重命名方法 {method} 存在")
            else:
                print(f"❌ 重命名方法 {method} 缺失")
                
        # 检查分割按钮
        if hasattr(window, 'split_selected_btn'):
            print("✅ 分割选中图像按钮存在")
        else:
            print("❌ 分割选中图像按钮缺失")
            
        if hasattr(window, 'batch_split_btn'):
            print("✅ 批量分割按钮存在")
        else:
            print("❌ 批量分割按钮缺失")
            
        # 显示窗口
        window.show()
        
        print("\n🎉 功能检查完成！")
        print("\n📋 手动测试建议:")
        print("1. 上传一张高度>2000px的图像")
        print("2. 选中图像，点击'🔪 分割选中图像'")
        print("3. 双击文件名测试重命名功能")
        print("4. 悬停缩略图测试重命名按钮")
        print("5. 测试批量分割功能")
        
        print("\n🔍 分割规则:")
        print("- 图像高度 > 2000px 才会分割")
        print("- 按2000px高度分段")
        print("- 保存到 temp_segments 目录")
        print("- 95% JPEG质量")
        
        print("\n✏️ 重命名交互:")
        print("- 双击文件名直接重命名（推荐）")
        print("- 悬停点击✏️按钮重命名")
        print("- 自动识别供应商代码")
        print("- 状态持久化保存")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_split_rename_features())
