#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
公共工具模块
提取重复代码，统一接口和实现
"""

import os
import json
import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Callable, Any
from PIL import Image
from datetime import datetime
from dataclasses import dataclass, asdict


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    issues: List[str]
    warnings: List[str]
    data: Optional[Dict] = None


class Logger:
    """统一日志接口"""
    
    def __init__(self, callback: Optional[Callable] = None):
        self.callback = callback
    
    def log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] [{level}] {message}"
        
        if self.callback:
            self.callback(log_entry)
        else:
            print(log_entry)
    
    def info(self, message: str):
        """信息日志"""
        self.log(message, "INFO")
    
    def warning(self, message: str):
        """警告日志"""
        self.log(message, "WARNING")
    
    def error(self, message: str):
        """错误日志"""
        self.log(message, "ERROR")


class FileUtils:
    """文件工具类"""
    
    @staticmethod
    def get_file_info(file_path: str) -> Dict:
        """获取文件信息"""
        try:
            info = {
                "path": file_path,
                "name": os.path.basename(file_path),
                "exists": os.path.exists(file_path),
                "size": None,
                "size_mb": None,
                "image_size": None,
                "is_image": False
            }
            
            if info["exists"]:
                file_size = os.path.getsize(file_path)
                info["size"] = file_size
                info["size_mb"] = file_size / (1024 * 1024)
                
                # 检查是否为图像文件
                image_extensions = ('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.tiff')
                if file_path.lower().endswith(image_extensions):
                    info["is_image"] = True
                    try:
                        with Image.open(file_path) as img:
                            info["image_size"] = img.size
                    except Exception:
                        pass
            
            return info
        except Exception as e:
            return {
                "path": file_path,
                "name": os.path.basename(file_path),
                "error": str(e),
                "exists": False
            }
    
    @staticmethod
    def validate_file_paths(file_paths: List[str]) -> ValidationResult:
        """验证文件路径"""
        issues = []
        warnings = []
        valid_files = []
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                issues.append(f"文件不存在: {file_path}")
            elif not os.path.isfile(file_path):
                issues.append(f"不是有效文件: {file_path}")
            else:
                valid_files.append(file_path)
                
                # 检查文件大小
                file_size = os.path.getsize(file_path)
                if file_size > 50 * 1024 * 1024:  # 50MB
                    warnings.append(f"文件过大: {os.path.basename(file_path)} ({file_size/1024/1024:.1f}MB)")
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            issues=issues,
            warnings=warnings,
            data={"valid_files": valid_files}
        )


class ConfigUtils:
    """配置工具类"""
    
    @staticmethod
    def load_json_config(file_path: str, default_config: Dict = None) -> Dict:
        """加载JSON配置文件"""
        if default_config is None:
            default_config = {}
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载配置文件失败 {file_path}: {e}")
        
        return default_config.copy()
    
    @staticmethod
    def save_json_config(file_path: str, config: Dict) -> bool:
        """保存JSON配置文件"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置文件失败 {file_path}: {e}")
            return False
    
    @staticmethod
    def merge_configs(base_config: Dict, override_config: Dict) -> Dict:
        """合并配置"""
        merged = base_config.copy()
        for key, value in override_config.items():
            if isinstance(value, dict) and key in merged and isinstance(merged[key], dict):
                merged[key] = ConfigUtils.merge_configs(merged[key], value)
            else:
                merged[key] = value
        return merged


class GUIUtils:
    """GUI工具类"""
    
    @staticmethod
    def create_scrollable_frame(parent, height: int = 200) -> tuple:
        """创建可滚动框架"""
        # 创建Canvas和Scrollbar
        canvas = tk.Canvas(parent, bg="white", height=height)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        return canvas, scrollbar, scrollable_frame
    
    @staticmethod
    def center_window(window, width: int = None, height: int = None):
        """居中显示窗口"""
        window.update_idletasks()
        
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    @staticmethod
    def create_button_frame(parent, buttons: List[tuple]) -> ttk.Frame:
        """创建按钮框架
        
        Args:
            parent: 父组件
            buttons: 按钮列表，格式为 [(text, command, side), ...]
        """
        frame = ttk.Frame(parent)
        
        for button_info in buttons:
            if len(button_info) >= 2:
                text, command = button_info[:2]
                side = button_info[2] if len(button_info) > 2 else tk.LEFT
                
                btn = ttk.Button(frame, text=text, command=command)
                btn.pack(side=side, padx=(0, 5) if side == tk.LEFT else (5, 0))
        
        return frame
    
    @staticmethod
    def create_info_section(parent, title: str, info_items: List[tuple]) -> ttk.LabelFrame:
        """创建信息显示区域
        
        Args:
            parent: 父组件
            title: 标题
            info_items: 信息项列表，格式为 [(label, value), ...]
        """
        frame = ttk.LabelFrame(parent, text=title, padding="10")
        
        for i, (label, value) in enumerate(info_items):
            row = i // 2
            col = (i % 2) * 2
            
            ttk.Label(frame, text=f"{label}:").grid(row=row, column=col, sticky="w", padx=(0, 5))
            ttk.Label(frame, text=str(value), font=("Arial", 9, "bold")).grid(
                row=row, column=col+1, sticky="w", padx=(0, 20)
            )
        
        return frame


class DataUtils:
    """数据处理工具类"""
    
    @staticmethod
    def merge_duplicate_items(items: List[Dict], key_fields: List[str] = None) -> List[Dict]:
        """合并重复项"""
        if key_fields is None:
            key_fields = ['name', 'price']
        
        # 按关键字段分组
        item_groups = {}
        for item in items:
            key = tuple(item.get(field, '') for field in key_fields)
            if key not in item_groups:
                item_groups[key] = []
            item_groups[key].append(item)
        
        # 合并重复项
        merged_items = []
        for group_items in item_groups.values():
            if len(group_items) == 1:
                merged_items.append(group_items[0])
            else:
                # 合并数量
                merged_item = group_items[0].copy()
                total_quantity = sum(item.get('quantity', 0) for item in group_items)
                merged_item['quantity'] = total_quantity
                merged_item['merged_from'] = len(group_items)
                merged_item['merge_time'] = datetime.now().isoformat()
                merged_items.append(merged_item)
        
        return merged_items
    
    @staticmethod
    def validate_item_data(items: List[Dict]) -> ValidationResult:
        """验证商品数据"""
        issues = []
        warnings = []
        
        for i, item in enumerate(items):
            item_name = f"商品 {i+1}"
            
            # 必需字段检查
            if not item.get('name'):
                issues.append(f"{item_name} 缺少名称")
            
            # 可选字段检查
            if not item.get('price'):
                warnings.append(f"{item_name} 缺少价格")
            if not item.get('quantity'):
                warnings.append(f"{item_name} 缺少数量")
            
            # 数据类型检查
            try:
                if item.get('price'):
                    float(item['price'])
            except (ValueError, TypeError):
                issues.append(f"{item_name} 价格格式错误")
            
            try:
                if item.get('quantity'):
                    int(item['quantity'])
            except (ValueError, TypeError):
                issues.append(f"{item_name} 数量格式错误")
        
        return ValidationResult(
            is_valid=len(issues) == 0,
            issues=issues,
            warnings=warnings,
            data={"total_items": len(items)}
        )
    
    @staticmethod
    def calculate_statistics(items: List[Dict]) -> Dict:
        """计算统计信息"""
        if not items:
            return {
                "total_items": 0,
                "total_amount": 0,
                "avg_price": 0,
                "avg_quantity": 0
            }
        
        total_items = len(items)
        total_amount = 0
        total_quantity = 0
        
        for item in items:
            price = float(item.get('price', 0))
            quantity = int(item.get('quantity', 0))
            total_amount += price * quantity
            total_quantity += quantity
        
        return {
            "total_items": total_items,
            "total_amount": total_amount,
            "avg_price": total_amount / total_quantity if total_quantity > 0 else 0,
            "avg_quantity": total_quantity / total_items if total_items > 0 else 0
        }


class ErrorHandler:
    """统一错误处理"""
    
    @staticmethod
    def safe_execute(func: Callable, *args, logger: Logger = None, **kwargs) -> tuple:
        """安全执行函数"""
        try:
            result = func(*args, **kwargs)
            return True, result
        except Exception as e:
            error_msg = f"执行 {func.__name__} 失败: {str(e)}"
            if logger:
                logger.error(error_msg)
            else:
                print(f"ERROR: {error_msg}")
            return False, str(e)
    
    @staticmethod
    def create_error_dialog(parent, title: str, message: str):
        """创建错误对话框"""
        from tkinter import messagebox
        messagebox.showerror(title, message)
    
    @staticmethod
    def create_warning_dialog(parent, title: str, message: str) -> bool:
        """创建警告对话框"""
        from tkinter import messagebox
        return messagebox.askyesno(title, message)


# 全局工具实例
default_logger = Logger()
file_utils = FileUtils()
config_utils = ConfigUtils()
gui_utils = GUIUtils()
data_utils = DataUtils()
error_handler = ErrorHandler()
