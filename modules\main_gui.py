#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主GUI模块
负责界面显示和用户交互
"""

import sys
import os
import threading
from datetime import datetime
from typing import Optional
import tkinter as tk
from tkinter import ttk, filedialog, scrolledtext
from PIL import Image, ImageTk

# 添加模块路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.config_manager import ConfigManager
from modules.file_manager import FileManager
from modules.ai_processor import AIProcessor
from modules.feishu_uploader import FeishuUploader
from modules.image_preview import ImagePreviewWindow
# 新增的增强功能模块
from modules.image_splitter import ImageSplitter, ImageInfo
from modules.supplier_manager import SupplierManager, SupplierGroupManager
from modules.supplier_tabs import SupplierTabManager
from modules.split_preview_dialog import show_split_preview
# 公共工具模块
from modules.common_utils import Logger, GUIUtils, ErrorHandler

class MainGUI:
    """主GUI界面"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能票据处理系统")
        self.root.geometry("1200x800")

        # 初始化模块（先不设置log回调）
        self.config_manager = ConfigManager()
        self.file_manager = FileManager()
        self.ai_processor = AIProcessor()
        self.feishu_uploader = FeishuUploader()

        # 新增的增强功能模块
        self.image_splitter = ImageSplitter()
        self.supplier_manager = SupplierManager()
        self.supplier_group_manager = SupplierGroupManager(self.supplier_manager)
        
        # 界面变量
        self.setup_variables()
        
        # 创建界面
        self.create_widgets()
        
        # 设置log回调（界面创建完成后）
        self.file_manager.log_callback = self.log_message
        self.ai_processor.log_callback = self.log_message
        self.feishu_uploader.log_callback = self.log_message

        # 加载配置
        self.load_config()

        # 绑定关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 设置剪贴板监听
        self.setup_clipboard_monitoring()

        # 初始化预览显示
        self.update_preview_display()
    
    def setup_variables(self):
        """设置界面变量"""
        self.app_id_var = tk.StringVar()
        self.app_secret_var = tk.StringVar()
        self.table_id_var = tk.StringVar()
        self.ai_api_key_var = tk.StringVar()
        self.ai_api_url_var = tk.StringVar()
        self.ai_model_var = tk.StringVar()

        self.current_prompt = ""
        self.processing = False

        # 新增的界面变量
        self.current_supplier_var = tk.StringVar()
        self.current_image_info: Optional[ImageInfo] = None
        self.supplier_tab_manager = None

        # 初始化统一日志器
        self.logger = Logger(callback=self.log_message_to_ui)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置区域
        self.create_config_section(main_frame)
        
        # 票据输入区域
        self.create_ticket_input_section(main_frame)
        
        # 处理控制区域
        self.create_control_section(main_frame)
        
        # 结果显示区域
        self.create_result_section(main_frame)

        # 日志区域
        self.create_log_section(main_frame)

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)  # 票据输入区域可扩展
        main_frame.rowconfigure(3, weight=1)  # 结果显示区域可扩展
        main_frame.rowconfigure(4, weight=1)  # 日志区域可扩展
    
    def create_config_section(self, parent):
        """创建配置区域"""
        config_frame = ttk.LabelFrame(parent, text="配置设置", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 飞书配置
        ttk.Label(config_frame, text="飞书App ID:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.app_id_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="飞书App Secret:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.app_secret_var, width=40, show="*").grid(row=1, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="电子表格ID:").grid(row=2, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.table_id_var, width=40).grid(row=2, column=1, sticky=(tk.W, tk.E))
        
        # AI配置
        ttk.Label(config_frame, text="AI API Key:").grid(row=3, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.ai_api_key_var, width=40, show="*").grid(row=3, column=1, sticky=(tk.W, tk.E))
        
        ttk.Label(config_frame, text="AI API URL:").grid(row=4, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.ai_api_url_var, width=40).grid(row=4, column=1, sticky=(tk.W, tk.E))

        ttk.Label(config_frame, text="AI模型名称:").grid(row=5, column=0, sticky=tk.W, padx=(0, 5))
        ttk.Entry(config_frame, textvariable=self.ai_model_var, width=40).grid(row=5, column=1, sticky=(tk.W, tk.E))

        # 按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=6, column=1, sticky=tk.E, pady=(5, 0))
        
        ttk.Button(button_frame, text="编辑提示词", command=self.edit_prompt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="供应商管理", command=self.manage_suppliers).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT)
        
        config_frame.columnconfigure(1, weight=1)
    
    def create_ticket_input_section(self, parent):
        """创建票据输入区域"""
        ticket_frame = ttk.LabelFrame(parent, text="智能票据输入", padding="10")
        ticket_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 顶部：供应商选择区域
        self.create_supplier_selection(ticket_frame)

        # 创建左右分栏
        content_frame = ttk.Frame(ticket_frame)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        left_frame = ttk.Frame(content_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))

        right_frame = ttk.Frame(content_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 左侧：上传区域
        self.create_upload_area(left_frame)

        # 右侧：预览区域
        self.create_preview_area(right_frame)

        # 配置网格权重
        content_frame.columnconfigure(0, weight=1)
        content_frame.columnconfigure(1, weight=1)
        content_frame.rowconfigure(0, weight=1)
    
    def create_upload_area(self, parent):
        """创建上传区域"""
        upload_frame = ttk.LabelFrame(parent, text="添加票据", padding="10")
        upload_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建带+号的上传框
        self.upload_area = tk.Frame(upload_frame, bg="#e8e8e8", relief="groove", bd=2, height=150)
        self.upload_area.pack(fill=tk.X, pady=(0, 10))
        self.upload_area.pack_propagate(False)
        
        # +号和提示文字
        upload_content = tk.Frame(self.upload_area, bg="#e8e8e8")
        upload_content.place(relx=0.5, rely=0.5, anchor="center")
        
        plus_label = tk.Label(upload_content, text="➕", font=("Arial", 24), bg="#e8e8e8", fg="#666")
        plus_label.pack()
        
        tip_label = tk.Label(upload_content, text="点击选择文件或拖拽到此处\n支持 Ctrl+V 粘贴图像", 
                           font=("Arial", 10), bg="#e8e8e8", fg="#666", justify=tk.CENTER)
        tip_label.pack()
        
        # 绑定点击事件
        for widget in [self.upload_area, upload_content, plus_label, tip_label]:
            widget.bind("<Button-1>", lambda e: self.select_files())
        
        # 操作按钮
        button_frame = ttk.Frame(upload_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(button_frame, text="📁 选择文件", command=self.select_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="📋 粘贴图像", command=self.paste_image).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="🗑️ 清空", command=self.clear_files).pack(side=tk.LEFT, padx=(0, 5))
    
    def create_preview_area(self, parent):
        """创建智能预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="智能预览", padding="5")
        preview_frame.pack(fill=tk.BOTH, expand=True)

        # 图像信息显示
        info_frame = ttk.Frame(preview_frame)
        info_frame.pack(fill=tk.X, pady=(0, 5))

        self.image_detail_label = ttk.Label(info_frame, text="请选择图像文件", font=("Arial", 9))
        self.image_detail_label.pack(side=tk.LEFT)

        # 分割建议按钮
        self.split_button = ttk.Button(info_frame, text="🔪 分割图像", command=self.show_split_preview,
                                      state="disabled")
        self.split_button.pack(side=tk.RIGHT)

        # 使用公共工具创建滚动区域
        self.preview_canvas, preview_scrollbar, self.preview_scrollable_frame = GUIUtils.create_scrollable_frame(
            preview_frame, height=200
        )

        self.preview_canvas.pack(side="left", fill="both", expand=True)
        preview_scrollbar.pack(side="right", fill="y")

    def create_supplier_selection(self, parent):
        """创建供应商选择区域"""
        supplier_frame = ttk.LabelFrame(parent, text="供应商信息", padding="5")
        supplier_frame.pack(fill=tk.X, pady=(0, 10))

        # 供应商选择
        ttk.Label(supplier_frame, text="选择供应商:").pack(side=tk.LEFT, padx=(0, 5))

        # 供应商下拉菜单
        self.supplier_combo = ttk.Combobox(supplier_frame, textvariable=self.current_supplier_var,
                                          width=15, state="readonly")
        self.supplier_combo.pack(side=tk.LEFT, padx=(0, 10))

        # 更新供应商列表
        self.update_supplier_list()

        # 新增供应商按钮
        ttk.Button(supplier_frame, text="新增供应商", command=self.add_new_supplier).pack(side=tk.LEFT, padx=(0, 5))

        # 当前图像信息显示
        self.image_info_label = ttk.Label(supplier_frame, text="未选择图像", foreground="gray")
        self.image_info_label.pack(side=tk.RIGHT)


    
    def create_control_section(self, parent):
        """创建处理控制区域"""
        control_frame = ttk.LabelFrame(parent, text="处理控制", padding="5")
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 处理按钮
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.LEFT, padx=(0, 10))
        
        self.ai_process_button = ttk.Button(button_frame, text="🤖 AI解析票据", command=self.start_ai_processing)
        self.ai_process_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.upload_button = ttk.Button(button_frame, text="📊 上传到飞书", command=self.start_feishu_upload)
        self.upload_button.pack(side=tk.LEFT, padx=(0, 5))
        
        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 0))
    
    def create_result_section(self, parent):
        """创建结果显示区域"""
        result_frame = ttk.LabelFrame(parent, text="AI解析结果", padding="5")
        result_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))

        # 创建左右分栏
        left_result_frame = ttk.Frame(result_frame)
        left_result_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5))

        right_result_frame = ttk.Frame(result_frame)
        right_result_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))

        # 左侧：AI原始响应
        ai_response_frame = ttk.LabelFrame(left_result_frame, text="AI原始响应", padding="5")
        ai_response_frame.pack(fill=tk.BOTH, expand=True)

        self.ai_response_text = scrolledtext.ScrolledText(ai_response_frame, height=12, width=50)
        self.ai_response_text.pack(fill=tk.BOTH, expand=True)

        # 右侧：解析结果表格
        table_frame = ttk.LabelFrame(right_result_frame, text="解析结果表格", padding="5")
        table_frame.pack(fill=tk.BOTH, expand=True)

        # 供应商前缀控制面板
        prefix_frame = ttk.Frame(table_frame)
        prefix_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(prefix_frame, text="供应商前缀:").pack(side=tk.LEFT, padx=(0, 5))

        # 供应商简称下拉菜单
        self.supplier_prefixes = ["GT408", "GT251", "GT155", "GT253", "DSD258", "DSD129", "DSD106", "GD340", "GT158", "自定义"]
        self.prefix_var = tk.StringVar()
        self.prefix_combo = ttk.Combobox(prefix_frame, textvariable=self.prefix_var,
                                        values=self.supplier_prefixes, state="readonly", width=10)
        self.prefix_combo.pack(side=tk.LEFT, padx=(0, 5))

        # 前缀操作按钮
        ttk.Button(prefix_frame, text="应用到当前", command=self.apply_prefix_to_current).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(prefix_frame, text="应用到全部", command=self.apply_prefix_to_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(prefix_frame, text="清除前缀", command=self.remove_prefix).pack(side=tk.LEFT, padx=(0, 5))

        # 创建表格
        self.create_result_table(table_frame)

        # 配置网格权重
        result_frame.columnconfigure(0, weight=1)
        result_frame.columnconfigure(1, weight=1)
        result_frame.rowconfigure(0, weight=1)

    def create_result_table(self, parent):
        """创建结果表格"""
        # 表格框架
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview表格
        columns = ("index", "filename", "supplier", "date", "type", "item_count", "status")
        self.result_tree = ttk.Treeview(table_container, columns=columns, show="headings", height=8)

        # 设置列标题
        column_headers = {
            "index": "索引",
            "filename": "文件名",
            "supplier": "供应商",
            "date": "日期",
            "type": "类型",
            "item_count": "商品数量",
            "status": "状态"
        }

        for col in columns:
            self.result_tree.heading(col, text=column_headers.get(col, col))
            self.result_tree.column(col, width=100, anchor="center")

        # 滚动条
        result_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.result_tree.yview)
        self.result_tree.configure(yscrollcommand=result_scrollbar.set)

        # 布局
        self.result_tree.pack(side="left", fill="both", expand=True)
        result_scrollbar.pack(side="right", fill="y")

        # 绑定选择事件
        self.result_tree.bind("<<TreeviewSelect>>", self.on_result_select)

        # 详细信息显示区域
        detail_frame = ttk.LabelFrame(parent, text="商品明细", padding="5")
        detail_frame.pack(fill=tk.X, pady=(10, 0))

        # 商品明细表格
        item_columns = ("款号", "颜色规格", "数量", "单价", "小计")
        self.item_tree = ttk.Treeview(detail_frame, columns=item_columns, show="headings", height=6)

        for col in item_columns:
            self.item_tree.heading(col, text=col)
            self.item_tree.column(col, width=80, anchor="center")

        item_scrollbar = ttk.Scrollbar(detail_frame, orient="vertical", command=self.item_tree.yview)
        self.item_tree.configure(yscrollcommand=item_scrollbar.set)

        self.item_tree.pack(side="left", fill="both", expand=True)
        item_scrollbar.pack(side="right", fill="y")

    def create_log_section(self, parent):
        """创建日志区域"""
        log_frame = ttk.LabelFrame(parent, text="处理日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def on_result_select(self, event):
        """处理结果表格选择事件"""
        selection = self.result_tree.selection()
        if not selection:
            return

        item = self.result_tree.item(selection[0])
        values = item['values']
        file_index = values[0] if values and len(values) > 0 else None

        if file_index is not None:
            # 显示对应文件的商品明细
            self.show_item_details(file_index)
            # 显示对应的AI响应
            ai_response = self.file_manager.get_ai_response(file_index)
            if ai_response:
                self.display_ai_response(ai_response)

    def show_item_details(self, file_index):
        """显示商品明细"""
        # 清空现有数据
        for item in self.item_tree.get_children():
            self.item_tree.delete(item)

        # 获取解析数据
        parsed_data = self.file_manager.get_parsed_data(file_index)
        if not parsed_data:
            self.log_message(f"文件索引 {file_index} 没有解析数据")
            return

        # 显示商品明细
        items = parsed_data.get('items', [])
        self.log_message(f"显示商品明细: {len(items)} 个商品")

        for i, item in enumerate(items):
            values = (
                item.get('款号', ''),
                item.get('颜色规格', ''),
                item.get('数量', ''),
                item.get('单价', ''),
                item.get('小计', '')
            )
            self.item_tree.insert('', 'end', values=values)
            self.log_message(f"商品 {i+1}: {values}")

    def display_ai_response(self, response_text: str):
        """显示AI原始响应"""
        self.ai_response_text.delete(1.0, tk.END)
        self.ai_response_text.insert(tk.END, response_text)
        self.ai_response_text.see(tk.END)

    def update_result_table(self):
        """更新结果表格"""
        # 清空现有数据
        for item in self.result_tree.get_children():
            self.result_tree.delete(item)

        # 获取文件列表
        files = self.file_manager.get_file_list()

        for i, file_path in enumerate(files):
            filename = os.path.basename(file_path)
            parsed_data = self.file_manager.get_parsed_data(i)

            if parsed_data:
                supplier = parsed_data.get('supplier', '未知')
                date = parsed_data.get('date', '未知')
                ticket_type = parsed_data.get('type', '未知')
                item_count = len(parsed_data.get('items', []))
                status = "✅ 已解析"
            else:
                supplier = date = ticket_type = "未解析"
                item_count = 0
                status = "⏳ 待解析"

            # 插入数据，第一列存储文件索引
            self.result_tree.insert('', 'end', values=(
                i, filename, supplier, date, ticket_type, item_count, status
            ))

        # 隐藏索引列
        self.result_tree.column("index", width=0, stretch=False)
        self.result_tree.heading("index", text="")

        # 如果有数据，自动选择第一行
        if self.result_tree.get_children():
            first_item = self.result_tree.get_children()[0]
            self.result_tree.selection_set(first_item)
            self.result_tree.focus(first_item)
            # 触发选择事件
            self.on_result_select(None)

    def log_message_to_ui(self, message: str):
        """在日志区域显示消息（UI回调）"""
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def log_message(self, message: str, level: str = "INFO"):
        """统一日志记录方法"""
        self.logger.log(message, level)

    def setup_clipboard_monitoring(self):
        """设置剪贴板监听"""
        self.root.bind('<Control-v>', self.on_paste_shortcut)
        self.root.focus_set()

    def on_paste_shortcut(self, event):
        """处理Ctrl+V快捷键"""
        self.paste_image()
        return "break"

    def select_files(self):
        """选择票据文件"""
        file_types = [
            ("图像文件", "*.jpg *.jpeg *.png *.gif *.bmp *.webp *.tiff"),
            ("PDF文件", "*.pdf"),
            ("所有文件", "*.*")
        ]

        files = filedialog.askopenfilenames(
            title="选择票据文件",
            filetypes=file_types
        )

        if files:
            self.file_manager.add_files(list(files))

            # 分析最后一个上传的图像
            if files:
                last_file = files[-1]
                if last_file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff')):
                    self.analyze_uploaded_image(last_file)

            self.update_preview_display()

    def paste_image(self):
        """粘贴图像"""
        if self.file_manager.paste_image_from_clipboard():
            self.update_preview_display()

    def clear_files(self):
        """清空文件"""
        self.file_manager.clear_all_files()
        self.update_preview_display()

    def remove_file(self, index: int):
        """删除指定文件"""
        if self.file_manager.remove_file(index):
            self.update_preview_display()

    def update_preview_display(self):
        """更新预览显示"""
        # 清空现有预览
        for widget in self.preview_scrollable_frame.winfo_children():
            widget.destroy()

        files = self.file_manager.get_file_list()

        if not files:
            # 显示空状态
            empty_label = tk.Label(self.preview_scrollable_frame, text="暂无图像\n请添加票据文件",
                                 font=("Arial", 10), fg="#999", justify=tk.CENTER)
            empty_label.pack(expand=True, fill=tk.BOTH, pady=20)
            return

        # 显示图像预览
        for i, file_path in enumerate(files):
            self.create_file_preview_item(i, file_path)

        # 更新滚动区域
        self.preview_canvas.update_idletasks()

    def create_file_preview_item(self, index: int, file_path: str):
        """创建文件预览项"""
        try:
            file_info = self.file_manager.get_file_info(file_path)

            # 创建图像框架
            img_frame = ttk.Frame(self.preview_scrollable_frame)
            img_frame.pack(fill=tk.X, padx=5, pady=5)

            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.tiff')):
                # 图像文件预览
                try:
                    # 加载和缩放图像
                    image = Image.open(file_path)
                    image.thumbnail((120, 120), Image.Resampling.LANCZOS)
                    photo = ImageTk.PhotoImage(image)

                    # 图像标签（可点击预览）
                    img_label = tk.Label(img_frame, image=photo, relief="solid", bd=1, cursor="hand2")
                    img_label.image = photo  # 保持引用
                    img_label.pack(side=tk.LEFT, padx=(0, 10))

                    # 绑定点击事件打开预览窗口
                    img_label.bind("<Button-1>", lambda e, path=file_path: self.open_image_preview(path))

                except Exception:
                    # 图像加载失败
                    error_label = tk.Label(img_frame, text="❌", font=("Arial", 20), fg="red")
                    error_label.pack(side=tk.LEFT, padx=(0, 10))
            else:
                # 非图像文件
                file_label = tk.Label(img_frame, text="📄", font=("Arial", 20))
                file_label.pack(side=tk.LEFT, padx=(0, 10))

            # 文件信息
            info_frame = ttk.Frame(img_frame)
            info_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # 文件名
            if file_info.get("is_clipboard"):
                name_text = f"📋 {file_info['name']}"
            else:
                name_text = f"📁 {file_info['name']}"

            name_label = tk.Label(info_frame, text=name_text, font=("Arial", 9, "bold"), anchor="w")
            name_label.pack(fill=tk.X)

            # 状态信息
            parsed_data = self.file_manager.get_parsed_data(index)
            if parsed_data:
                status_text = f"✅ 已解析 - 供应商: {parsed_data.get('supplier', '未知')}"
                status_color = "green"
            else:
                status_text = "⏳ 待解析"
                status_color = "orange"

            status_label = tk.Label(info_frame, text=status_text,
                                  font=("Arial", 8), fg=status_color, anchor="w")
            status_label.pack(fill=tk.X)

            # 图像尺寸信息
            if file_info.get("image_size"):
                size_text = f"尺寸: {file_info['image_size'][0]}×{file_info['image_size'][1]}"
                size_label = tk.Label(info_frame, text=size_text,
                                    font=("Arial", 8), fg="#666", anchor="w")
                size_label.pack(fill=tk.X)

            # 删除按钮
            delete_btn = tk.Button(info_frame, text="❌", font=("Arial", 8),
                                 command=lambda idx=index: self.remove_file(idx),
                                 bg="#ffeeee", relief="flat")
            delete_btn.pack(anchor="w", pady=(5, 0))

        except Exception as e:
            # 错误显示
            error_frame = ttk.Frame(self.preview_scrollable_frame)
            error_frame.pack(fill=tk.X, padx=5, pady=5)

            error_label = tk.Label(error_frame, text=f"❌ {os.path.basename(file_path)} (加载失败)",
                                 font=("Arial", 9), fg="red", anchor="w")
            error_label.pack(fill=tk.X)

    def save_config(self):
        """保存配置"""
        config = {
            "feishu": {
                "app_id": self.app_id_var.get(),
                "app_secret": self.app_secret_var.get(),
                "table_id": self.table_id_var.get()
            },
            "ai": {
                "api_key": self.ai_api_key_var.get(),
                "api_url": self.ai_api_url_var.get(),
                "model": self.ai_model_var.get()
            },
            "prompt": self.current_prompt
        }

        if self.config_manager.save_config(config):
            self.log_message("配置已保存")
            # 更新处理器配置
            self.update_processors_config()
        else:
            self.log_message("保存配置失败")

    def load_config(self):
        """加载配置"""
        config = self.config_manager.load_config()

        # 加载飞书配置
        feishu_config = config.get("feishu", {})
        self.app_id_var.set(feishu_config.get("app_id", ""))
        self.app_secret_var.set(feishu_config.get("app_secret", ""))
        self.table_id_var.set(feishu_config.get("table_id", ""))

        # 加载AI配置
        ai_config = config.get("ai", {})
        self.ai_api_key_var.set(ai_config.get("api_key", ""))
        self.ai_api_url_var.set(ai_config.get("api_url", ""))
        self.ai_model_var.set(ai_config.get("model", ""))

        # 加载提示词
        self.current_prompt = config.get("prompt", "")

        # 更新处理器配置
        self.update_processors_config()

        self.log_message("配置已加载")

    def update_processors_config(self):
        """更新处理器配置"""
        # 更新AI处理器
        self.ai_processor.update_config(
            api_key=self.ai_api_key_var.get(),
            api_url=self.ai_api_url_var.get(),
            model_name=self.ai_model_var.get(),
            prompt_template=self.current_prompt
        )

        # 更新飞书上传器
        self.feishu_uploader.update_config(
            app_id=self.app_id_var.get(),
            app_secret=self.app_secret_var.get()
        )

    def validate_config(self) -> bool:
        """验证配置"""
        config = {
            "feishu": {
                "app_id": self.app_id_var.get(),
                "app_secret": self.app_secret_var.get(),
                "table_id": self.table_id_var.get()
            },
            "ai": {
                "api_key": self.ai_api_key_var.get(),
                "api_url": self.ai_api_url_var.get()
            }
        }

        is_valid, message = self.config_manager.validate_config(config)
        if not is_valid:
            self.log_message(f"配置验证失败: {message}")

        return is_valid

    def edit_prompt(self):
        """编辑提示词"""
        # 创建提示词编辑窗口
        prompt_window = tk.Toplevel(self.root)
        prompt_window.title("编辑提示词")
        prompt_window.geometry("800x600")
        prompt_window.transient(self.root)
        prompt_window.grab_set()

        # 主框架
        main_frame = ttk.Frame(prompt_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明标签
        info_label = ttk.Label(main_frame, text="请编辑票据识别的提示词模板：")
        info_label.pack(anchor=tk.W, pady=(0, 5))

        # 提示词文本框
        prompt_text = scrolledtext.ScrolledText(main_frame, height=25, width=80)
        prompt_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 加载当前提示词
        if self.current_prompt:
            prompt_text.insert(tk.END, self.current_prompt)
        else:
            # 加载默认提示词
            default_prompt = self.config_manager.get_default_prompt()
            prompt_text.insert(tk.END, default_prompt)

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def save_prompt():
            """保存提示词"""
            self.current_prompt = prompt_text.get("1.0", tk.END).strip()
            self.log_message("提示词已更新")
            prompt_window.destroy()

        def reset_prompt():
            """重置为默认提示词"""
            prompt_text.delete("1.0", tk.END)
            prompt_text.insert(tk.END, self.config_manager.get_default_prompt())

        ttk.Button(button_frame, text="重置默认", command=reset_prompt).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="取消", command=prompt_window.destroy).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="保存", command=save_prompt).pack(side=tk.RIGHT)

    def apply_prefix_to_current(self):
        """应用供应商前缀到当前选中文件"""
        selected_prefix = self.prefix_var.get()
        if not selected_prefix:
            self.log_message("请选择供应商前缀")
            return

        # 获取当前选中的文件
        selection = self.result_tree.selection()
        if not selection:
            self.log_message("请先选择要应用前缀的文件")
            return

        item = self.result_tree.item(selection[0])
        values = item['values']
        file_index = values[0] if values and len(values) > 0 else None

        if file_index is None:
            self.log_message("无法获取文件索引")
            return

        if selected_prefix == "自定义":
            # 弹出自定义前缀输入对话框
            custom_prefix = self.get_custom_prefix()
            if not custom_prefix:
                return
            selected_prefix = custom_prefix

        # 应用前缀到当前文件
        if self.file_manager.add_prefix_to_items(file_index, selected_prefix):
            filename = os.path.basename(self.file_manager.get_file_list()[file_index])
            self.log_message(f"已给文件 {filename} 的款号添加前缀: {selected_prefix}")
            # 更新显示
            self.update_result_table()
            self.on_result_select(None)
        else:
            self.log_message("没有需要添加前缀的数据")

    def apply_prefix_to_all(self):
        """应用供应商前缀到所有文件"""
        selected_prefix = self.prefix_var.get()
        if not selected_prefix:
            self.log_message("请选择供应商前缀")
            return

        if selected_prefix == "自定义":
            # 弹出自定义前缀输入对话框
            custom_prefix = self.get_custom_prefix()
            if not custom_prefix:
                return
            selected_prefix = custom_prefix

        # 应用前缀到所有文件
        modified_count = self.file_manager.add_prefix_to_all_items(selected_prefix)

        if modified_count > 0:
            self.log_message(f"已给 {modified_count} 个文件的款号添加前缀: {selected_prefix}")
            # 更新显示
            self.update_result_table()
            # 如果有选中的行，更新商品明细
            selection = self.result_tree.selection()
            if selection:
                self.on_result_select(None)
        else:
            self.log_message("没有需要添加前缀的数据")

    def remove_prefix(self):
        """移除供应商前缀"""
        modified_count = self.file_manager.remove_prefix_from_all_items()

        if modified_count > 0:
            self.log_message(f"已移除 {modified_count} 个文件的款号前缀")
            # 更新显示
            self.update_result_table()
            # 如果有选中的行，更新商品明细
            selection = self.result_tree.selection()
            if selection:
                self.on_result_select(None)
        else:
            self.log_message("没有需要移除前缀的数据")

    def get_custom_prefix(self) -> str:
        """获取自定义前缀"""
        # 创建自定义前缀输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("自定义供应商前缀")
        dialog.geometry("300x150")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = {"prefix": ""}

        # 主框架
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明标签
        ttk.Label(main_frame, text="请输入自定义供应商前缀:").pack(pady=(0, 10))

        # 输入框
        prefix_entry = ttk.Entry(main_frame, width=20, font=("Arial", 12))
        prefix_entry.pack(pady=(0, 10))
        prefix_entry.focus()

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def confirm():
            prefix = prefix_entry.get().strip()
            if prefix:
                # 验证前缀格式（只允许字母和数字）
                if prefix.replace("-", "").replace("_", "").isalnum():
                    result["prefix"] = prefix
                    dialog.destroy()
                else:
                    self.log_message("前缀只能包含字母、数字、下划线和连字符")
            else:
                self.log_message("请输入前缀")

        def cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="确定", command=confirm).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.RIGHT)

        # 绑定回车键
        prefix_entry.bind("<Return>", lambda e: confirm())

        # 等待对话框关闭
        dialog.wait_window()

        return result["prefix"]

    def open_image_preview(self, image_path: str):
        """打开图像预览窗口"""
        try:
            ImagePreviewWindow(self.root, image_path)
        except Exception as e:
            self.log_message(f"打开图像预览失败: {str(e)}")

    def start_ai_processing(self):
        """开始AI处理"""
        if self.processing:
            self.log_message("正在处理中，请等待...")
            return

        files = self.file_manager.get_file_list()
        if not files:
            self.log_message("请先添加票据文件")
            return

        if not self.validate_config():
            return

        # 禁用按钮
        self.ai_process_button.config(state="disabled")
        self.processing = True

        # 在新线程中处理
        thread = threading.Thread(target=self._ai_processing_thread)
        thread.daemon = True
        thread.start()

    def _ai_processing_thread(self):
        """AI处理线程"""
        try:
            files = self.file_manager.get_file_list()
            total = len(files)

            self.log_message(f"开始AI解析 {total} 张票据")

            for i, file_path in enumerate(files):
                # 更新进度
                progress = (i / total) * 100
                self.progress_var.set(progress)

                filename = os.path.basename(file_path)
                self.log_message(f"正在解析: {filename}")

                # AI分析
                result, ai_response = self.ai_processor.analyze_ticket(file_path)

                # 保存AI原始响应
                self.file_manager.set_ai_response(i, ai_response)

                # 显示AI响应（只显示最后一个文件的响应）
                if i == len(files) - 1:
                    self.display_ai_response(ai_response)

                if result:
                    # 验证数据格式
                    is_valid, message = self.ai_processor.validate_ticket_data(result)
                    if is_valid:
                        self.file_manager.set_parsed_data(i, result)
                        self.log_message(f"✅ {filename} 解析成功")
                    else:
                        self.log_message(f"❌ {filename} 数据格式错误: {message}")
                else:
                    self.log_message(f"❌ {filename} 解析失败")

                # 更新预览显示和结果表格
                self.root.after(0, self.update_preview_display)
                self.root.after(0, self.update_result_table)

            # 完成处理
            self.progress_var.set(100)
            success_count = len(self.file_manager.get_all_parsed_data())
            self.log_message(f"AI解析完成: {success_count}/{total} 成功")

        except Exception as e:
            self.log_message(f"AI处理异常: {str(e)}")

        finally:
            # 重新启用按钮
            self.ai_process_button.config(state="normal")
            self.processing = False
            self.progress_var.set(0)

    def start_feishu_upload(self):
        """开始飞书上传"""
        if self.processing:
            self.log_message("正在处理中，请等待...")
            return

        if not self.file_manager.has_parsed_data():
            self.log_message("请先进行AI解析")
            return

        if not self.validate_config():
            return

        # 禁用按钮
        self.upload_button.config(state="disabled")
        self.processing = True

        # 在新线程中处理
        thread = threading.Thread(target=self._feishu_upload_thread)
        thread.daemon = True
        thread.start()

    def _feishu_upload_thread(self):
        """飞书上传线程"""
        try:
            parsed_data_list = self.file_manager.get_all_parsed_data()
            table_id = self.table_id_var.get()

            self.log_message(f"开始上传 {len(parsed_data_list)} 张票据到飞书")

            def progress_callback(current, total, success):
                progress = (current / total) * 100
                self.progress_var.set(progress)

            success_count, total_count = self.feishu_uploader.batch_upload_tickets(
                table_id, parsed_data_list, progress_callback
            )

            self.log_message(f"飞书上传完成: {success_count}/{total_count} 成功")

        except Exception as e:
            self.log_message(f"飞书上传异常: {str(e)}")

        finally:
            # 重新启用按钮
            self.upload_button.config(state="normal")
            self.processing = False
            self.progress_var.set(0)

    def on_closing(self):
        """应用关闭时的处理"""
        try:
            # 停止所有正在进行的处理
            self.processing = False

            # 清理临时文件
            if hasattr(self, 'file_manager'):
                self.file_manager.cleanup_temp_files()

            # 强制结束所有后台线程
            import threading
            for thread in threading.enumerate():
                if thread != threading.current_thread() and thread.daemon:
                    try:
                        thread.join(timeout=1.0)  # 等待1秒
                    except:
                        pass

            # 销毁窗口
            self.root.quit()  # 先退出主循环
            self.root.destroy()  # 再销毁窗口

        except Exception as e:
            # 如果正常关闭失败，强制退出
            import os
            os._exit(0)

    def run(self):
        """运行GUI应用"""
        self.root.mainloop()

    # ==================== 新增的增强功能方法 ====================

    def update_supplier_list(self):
        """更新供应商列表"""
        try:
            suppliers = self.supplier_manager.get_all_suppliers()
            supplier_names = [f"{s.code} - {s.name}" for s in suppliers]
            self.supplier_combo['values'] = supplier_names

            if supplier_names and not self.current_supplier_var.get():
                self.supplier_combo.current(0)
        except Exception as e:
            self.log_message(f"更新供应商列表失败: {str(e)}")

    def get_current_supplier_code(self) -> str:
        """获取当前选中的供应商代码"""
        current_value = self.current_supplier_var.get()
        if current_value and " - " in current_value:
            return current_value.split(" - ")[0]
        return ""

    def add_new_supplier(self):
        """添加新供应商"""
        try:
            # 简单的输入对话框
            from tkinter import simpledialog, messagebox

            code = simpledialog.askstring("新增供应商", "请输入供应商代码:")
            if not code:
                return

            name = simpledialog.askstring("新增供应商", "请输入供应商名称:")
            if not name:
                return

            if self.supplier_manager.add_supplier(code, name):
                self.update_supplier_list()
                # 选择新添加的供应商
                for i, value in enumerate(self.supplier_combo['values']):
                    if value.startswith(code):
                        self.supplier_combo.current(i)
                        break
                self.log_message(f"成功添加供应商: {code} - {name}")
            else:
                messagebox.showerror("错误", "供应商代码已存在")

        except Exception as e:
            self.log_message(f"添加供应商失败: {str(e)}")

    def manage_suppliers(self):
        """管理供应商"""
        try:
            # 简单的供应商列表显示
            from tkinter import messagebox
            suppliers = self.supplier_manager.get_all_suppliers()

            info = "当前供应商列表:\n\n"
            for supplier in suppliers:
                info += f"{supplier.code} - {supplier.name} (使用次数: {supplier.usage_count})\n"

            messagebox.showinfo("供应商管理", info)

        except Exception as e:
            self.log_message(f"供应商管理失败: {str(e)}")

    def analyze_uploaded_image(self, file_path: str):
        """分析上传的图像"""
        try:
            supplier_code = self.get_current_supplier_code()
            if not supplier_code:
                self.log_message("请先选择供应商")
                return

            # 分析图像
            image_info = self.image_splitter.analyze_image(file_path, supplier_code)
            self.current_image_info = image_info
            self.update_image_info_display(image_info)

            self.log_message(f"图像分析完成: {os.path.basename(file_path)} ({image_info.width}×{image_info.height})")

        except Exception as e:
            self.log_message(f"图像分析失败: {str(e)}")
            # 即使分析失败，也要设置基本的图像信息以便分割功能工作
            try:
                from PIL import Image
                img = Image.open(file_path)
                width, height = img.size
                # 创建基本的ImageInfo对象
                from modules.image_splitter import ImageInfo
                self.current_image_info = ImageInfo(
                    file_path=file_path,
                    width=width,
                    height=height,
                    size_mb=os.path.getsize(file_path) / (1024 * 1024),
                    needs_split=height > 2000,
                    recommended_segments=max(2, (height + 2000 - 1) // 2000) if height > 2000 else 1,
                    supplier_code=self.get_current_supplier_code()
                )
                self.update_image_info_display(self.current_image_info)
            except Exception as e2:
                self.log_message(f"创建基本图像信息失败: {str(e2)}")

    def update_image_info_display(self, image_info: ImageInfo):
        """更新图像信息显示"""
        try:
            info_text = f"尺寸: {image_info.width}×{image_info.height} | "
            info_text += f"大小: {image_info.size_mb:.1f}MB"

            if image_info.needs_split:
                info_text += f" | ⚠️ 建议分割为 {image_info.recommended_segments} 段"
                self.split_button.config(state="normal")
                self.image_info_label.config(text=info_text, foreground="orange")
            else:
                info_text += " | ✅ 无需分割"
                self.split_button.config(state="disabled")
                self.image_info_label.config(text=info_text, foreground="green")

            self.image_detail_label.config(text=info_text)
        except Exception as e:
            self.log_message(f"更新图像信息显示失败: {str(e)}")

    def show_split_preview(self):
        """显示分割预览"""
        if not self.current_image_info:
            self.log_message("没有可分割的图像")
            return

        try:
            # 计算分割点
            split_points = self.image_splitter.calculate_split_points(self.current_image_info.height)

            def on_confirm(confirmed_split_points):
                self.execute_image_split(self.current_image_info.file_path,
                                       self.current_image_info.supplier_code,
                                       confirmed_split_points)

            def on_cancel():
                self.log_message("用户取消了图像分割")

            # 显示分割预览对话框
            show_split_preview(self.root, self.current_image_info.file_path,
                              split_points, on_confirm, on_cancel)
        except Exception as e:
            self.log_message(f"显示分割预览失败: {str(e)}")

    def execute_image_split(self, image_path: str, supplier_code: str, split_points):
        """执行图像分割"""
        try:
            self.log_message(f"开始分割图像: {os.path.basename(image_path)}")

            # 执行分割
            segments = self.image_splitter.split_image(image_path, supplier_code, split_points)

            # 验证分割质量
            validation = self.image_splitter.validate_split_quality(segments)
            if not validation['is_valid']:
                from tkinter import messagebox
                messagebox.showwarning("分割质量警告",
                                     f"分割质量检查发现问题:\n" + "\n".join(validation['issues']))

            # 将分割后的文件添加到文件管理器
            for segment in segments:
                self.file_manager.add_files([segment.file_path])

            self.log_message(f"图像分割完成，生成 {len(segments)} 个分段")
            self.update_preview_display()

        except Exception as e:
            self.log_message(f"图像分割失败: {str(e)}")

    def on_supplier_tab_changed(self, supplier_code: str):
        """供应商标签页切换事件"""
        self.log_message(f"切换到供应商: {supplier_code}")

    def update_supplier_results(self, supplier_code: str, items: list):
        """更新供应商结果"""
        try:
            if self.supplier_tab_manager:
                supplier = self.supplier_manager.get_supplier(supplier_code)
                supplier_name = supplier.name if supplier else supplier_code
                self.supplier_tab_manager.add_tab(supplier_code, supplier_name, items)
        except Exception as e:
            self.log_message(f"更新供应商结果失败: {str(e)}")


if __name__ == "__main__":
    app = MainGUI()
    app.run()
