#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书上传模块
负责将数据上传到飞书电子表格
"""

import datetime
from typing import Dict, List, Optional, Callable
import lark_oapi as lark
import requests

class FeishuUploader:
    """飞书上传器"""
    
    def __init__(self, app_id: str = "", app_secret: str = "", 
                 log_callback: Optional[Callable] = None):
        self.app_id = app_id
        self.app_secret = app_secret
        self.log_callback = log_callback or self._default_log
        self.feishu_client = None
        self._init_client()
    
    def _default_log(self, message: str):
        """默认日志输出"""
        print(f"[FeishuUploader] {message}")
    
    def log(self, message: str):
        """记录日志"""
        self.log_callback(message)
    
    def _init_client(self):
        """初始化飞书客户端"""
        try:
            if self.app_id and self.app_secret:
                self.feishu_client = lark.Client.builder() \
                    .app_id(self.app_id) \
                    .app_secret(self.app_secret) \
                    .build()
                self.log("飞书客户端初始化成功")
            else:
                self.log("飞书配置不完整，客户端未初始化")
        except Exception as e:
            self.log(f"飞书客户端初始化失败: {str(e)}")
    
    def update_config(self, app_id: str, app_secret: str):
        """更新飞书配置"""
        self.app_id = app_id
        self.app_secret = app_secret
        self._init_client()
    
    def upload_ticket_data(self, table_id: str, ticket_data: Dict) -> bool:
        """
        将单个票据数据上传到飞书电子表格
        
        Args:
            table_id: 飞书电子表格ID
            ticket_data: 票据数据
            
        Returns:
            上传是否成功
        """
        try:
            if not self.feishu_client:
                self.log("飞书客户端未初始化")
                return False
            
            # 生成唯一票据ID
            ticket_id = f"T{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"
            current_time = datetime.datetime.now().isoformat()
            
            # 构建要插入的数据行
            rows_data = []
            for item in ticket_data.get('items', []):
                row_data = [
                    ticket_id,  # 票据ID
                    ticket_data.get('supplier', ''),  # 供应商
                    ticket_data.get('date', ''),  # 日期
                    ticket_data.get('type', '销售'),  # 类型
                    item.get('款号', ''),  # 款号
                    item.get('颜色规格', ''),  # 颜色规格
                    item.get('数量', 0),  # 数量
                    item.get('单价', 0.0),  # 单价
                    item.get('小计', 0.0),  # 小计
                    current_time,  # 处理时间
                    "待审核"  # 状态
                ]
                rows_data.append(row_data)
            
            # 获取访问令牌
            access_token = self._get_access_token()
            if not access_token:
                self.log("获取访问令牌失败")
                return False
            
            # 使用HTTP API插入数据
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # 构建请求数据
            payload = {
                "valueRange": {
                    "range": "A:K",  # 11列数据
                    "values": rows_data
                }
            }

            # 发送请求到飞书电子表格API
            api_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{table_id}/values_append"

            response = requests.post(api_url, headers=headers, json=payload)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    self.log(f"成功上传 {len(ticket_data.get('items', []))} 条记录到飞书电子表格")
                    return True
                else:
                    self.log(f"上传失败: {result.get('msg')}")
                    return False
            else:
                self.log(f"上传失败: {response.status_code} - {response.text}")
                return False
            
        except Exception as e:
            self.log(f"上传数据异常: {str(e)}")
            return False
    
    def batch_upload_tickets(self, table_id: str, tickets_data: List[Dict], 
                           progress_callback: Optional[Callable] = None) -> tuple[int, int]:
        """
        批量上传票据数据
        
        Args:
            table_id: 飞书电子表格ID
            tickets_data: 票据数据列表
            progress_callback: 进度回调函数，接收(current, total, success)参数
            
        Returns:
            (成功数量, 总数量)
        """
        success_count = 0
        total_count = len(tickets_data)
        
        self.log(f"开始批量上传 {total_count} 张票据")
        
        for i, ticket_data in enumerate(tickets_data):
            if ticket_data is None:
                self.log(f"跳过第 {i+1} 张票据（数据为空）")
                if progress_callback:
                    progress_callback(i + 1, total_count, False)
                continue
            
            self.log(f"上传进度: {i+1}/{total_count}")
            
            success = self.upload_ticket_data(table_id, ticket_data)
            if success:
                success_count += 1
            
            if progress_callback:
                progress_callback(i + 1, total_count, success)
        
        self.log(f"批量上传完成: {success_count}/{total_count} 成功")
        return success_count, total_count
    
    def _get_access_token(self) -> Optional[str]:
        """获取飞书访问令牌"""
        try:
            if not self.app_id or not self.app_secret:
                self.log("飞书配置不完整")
                return None

            # 使用HTTP API获取访问令牌
            url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
            payload = {
                "app_id": self.app_id,
                "app_secret": self.app_secret
            }

            response = requests.post(url, json=payload)

            if response.status_code == 200:
                result = response.json()
                if result.get("code") == 0:
                    return result.get("tenant_access_token")
                else:
                    self.log(f"获取访问令牌失败: {result.get('msg')}")
                    return None
            else:
                self.log(f"获取访问令牌请求失败: {response.status_code}")
                return None

        except Exception as e:
            self.log(f"获取访问令牌失败: {str(e)}")
            return None
    
    def test_connection(self, table_id: str) -> bool:
        """测试飞书连接"""
        try:
            if not self.feishu_client:
                self.log("飞书客户端未初始化")
                return False
            
            # 尝试获取表格信息来测试连接
            access_token = self._get_access_token()
            if not access_token:
                return False
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            # 测试API调用
            api_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{table_id}"
            response = requests.get(api_url, headers=headers)
            
            if response.status_code == 200:
                self.log("飞书连接测试成功")
                return True
            else:
                self.log(f"飞书连接测试失败: {response.status_code}")
                return False
            
        except Exception as e:
            self.log(f"飞书连接测试异常: {str(e)}")
            return False
    
    def create_table_headers(self, table_id: str) -> bool:
        """创建表格标题行"""
        try:
            headers_data = [[
                "票据ID", "供应商", "日期", "类型", "款号", 
                "颜色规格", "数量", "单价", "小计", "处理时间", "状态"
            ]]
            
            access_token = self._get_access_token()
            if not access_token:
                return False
            
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "valueRange": {
                    "range": f"{table_id}!A1:K1",
                    "values": headers_data
                }
            }
            
            api_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{table_id}/values_update"
            response = requests.put(api_url, headers=headers, json=payload)
            
            if response.status_code == 200:
                self.log("表格标题行创建成功")
                return True
            else:
                self.log(f"创建标题行失败: {response.status_code}")
                return False
            
        except Exception as e:
            self.log(f"创建标题行异常: {str(e)}")
            return False
