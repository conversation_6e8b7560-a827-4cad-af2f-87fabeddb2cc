#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能票据处理系统 - 主启动文件 (PyQt5版本)
现代化界面设计，支持黑色主题和标签页布局
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('PyQt5', 'PyQt5界面库'),
        ('requests', '网络请求库'),
        ('PIL', 'Python图像库'),
        ('lark_oapi', '飞书API SDK')
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'PyQt5':
                import PyQt5
            else:
                __import__(package)
            print(f"✅ {description} 已安装")
        except ImportError:
            print(f"❌ {description} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包：{', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 智能票据处理系统 - PyQt5版本")
    print("=" * 60)
    print("✨ 功能特性:")
    print("- 🔧 模块化代码结构")
    print("- 🤖 AI解析和飞书上传分离")
    print("- 📋 支持剪贴板粘贴图像")
    print("- 🖼️ 实时图像预览")
    print("- ⚙️ 可视化配置管理")
    print("- 📊 详细的处理日志")
    print("- 🔪 智能图像分割功能")
    print("- 🏢 供应商分组管理")
    print("- 📑 多标签页结果显示")
    print("- 🎯 批量智能处理")
    print("=" * 60)
    
    # 检查依赖
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    try:
        # 导入并启动PyQt5 GUI
        print("\n🚀 启动应用...")
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI

        # 设置高DPI支持（必须在QApplication创建之前）
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("智能票据处理系统")
        app.setApplicationVersion("2.0 PyQt5版")
        app.setOrganizationName("智能票据处理")

        print("✅ 依赖检查通过")
        print("🎨 应用黑色主题")
        print("📱 创建现代化界面")

        # 创建主窗口
        window = ModernTicketProcessorGUI()
        window.show()

        print("🎉 界面启动成功!")
        print("\n界面特性:")
        print("  • 🌙 深色主题设计")
        print("  • 📑 标签页布局")
        print("  • 📊 左右分栏显示")
        print("  • 🔧 现代化配置界面")
        print("  • 📈 实时进度显示")
        print("  • 🎯 优化的用户体验")

        print("\n使用说明:")
        print("1. 在'配置设置'标签页配置AI和飞书参数")
        print("2. 在'主要功能'标签页选择票据图像文件")
        print("3. 点击'🤖 AI解析票据'")
        print("4. 查看右侧解析结果")
        print("5. 点击'📊 上传到飞书'")

        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print("请确保所有模块文件都在正确位置")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
