#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像分割预览对话框
"""

import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk, ImageDraw
from typing import List, Dict, Callable, Optional
from modules.image_splitter import SplitPoint


class SplitPreviewDialog:
    """图像分割预览对话框"""
    
    def __init__(self, parent, image_path: str, split_points: List[SplitPoint], 
                 on_confirm: Callable = None, on_cancel: Callable = None):
        self.parent = parent
        self.image_path = image_path
        self.split_points = split_points.copy()
        self.on_confirm = on_confirm
        self.on_cancel = on_cancel
        
        self.dialog = None
        self.canvas = None
        self.image = None
        self.photo = None
        self.canvas_image = None
        self.scale_factor = 1.0
        self.canvas_width = 600
        self.canvas_height = 400
        
        # 分割线相关
        self.split_lines = []
        self.dragging_line = None
        self.drag_start_y = 0
        
        self.create_dialog()
        self.load_and_display_image()
        
    def create_dialog(self):
        """创建对话框"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("图像分割预览")
        self.dialog.geometry("900x700")  # 增加对话框大小
        self.dialog.transient(self.parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (450)  # 使用固定值避免计算错误
        y = (self.dialog.winfo_screenheight() // 2) - (350)
        self.dialog.geometry(f"900x700+{x}+{y}")

        # 创建主框架
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 顶部信息
        self.create_info_section(main_frame)

        # 中间预览区域
        self.create_preview_section(main_frame)

        # 底部控制区域
        self.create_control_section(main_frame)

        # 绑定关闭事件
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel_click)
    
    def create_info_section(self, parent):
        """创建信息区域"""
        info_frame = ttk.LabelFrame(parent, text="分割信息")
        info_frame.pack(fill=tk.X, pady=(0, 10))
        
        info_content = ttk.Frame(info_frame)
        info_content.pack(fill=tk.X, padx=10, pady=10)
        
        # 图像信息
        try:
            img = Image.open(self.image_path)
            width, height = img.size
            
            ttk.Label(info_content, text=f"图像尺寸: {width} × {height} 像素").pack(anchor="w")
            ttk.Label(info_content, text=f"建议分割: {len(self.split_points) + 1} 段").pack(anchor="w")
            ttk.Label(info_content, text="提示: 拖拽红色分割线可调整分割位置").pack(anchor="w")
            
        except Exception as e:
            ttk.Label(info_content, text=f"无法读取图像信息: {e}").pack(anchor="w")
    
    def create_preview_section(self, parent):
        """创建预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="分割预览")
        preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建画布框架
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建画布和滚动条
        self.canvas = tk.Canvas(canvas_frame, bg="white", 
                               width=self.canvas_width, height=self.canvas_height)
        
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # 绑定鼠标事件
        self.canvas.bind("<Button-1>", self.on_canvas_click)
        self.canvas.bind("<B1-Motion>", self.on_canvas_drag)
        self.canvas.bind("<ButtonRelease-1>", self.on_canvas_release)
        self.canvas.bind("<Motion>", self.on_canvas_motion)
    
    def create_control_section(self, parent):
        """创建控制区域"""
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, pady=(10, 0))

        # 左侧：分割点列表
        left_frame = ttk.LabelFrame(control_frame, text="分割点管理")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 分割点列表
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        self.split_listbox = tk.Listbox(list_frame, height=4)  # 减少高度
        list_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.split_listbox.yview)
        self.split_listbox.configure(yscrollcommand=list_scroll.set)

        self.split_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        list_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 分割点操作按钮
        btn_frame = ttk.Frame(left_frame)
        btn_frame.pack(fill=tk.X, padx=10, pady=(5, 10))

        ttk.Button(btn_frame, text="添加分割点", command=self.add_split_point).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="删除分割点", command=self.remove_split_point).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(btn_frame, text="重置", command=self.reset_split_points).pack(side=tk.LEFT)

        # 右侧：主要操作按钮
        right_frame = ttk.LabelFrame(control_frame, text="操作")
        right_frame.pack(side=tk.RIGHT, padx=(10, 0))

        # 按钮容器
        button_container = ttk.Frame(right_frame)
        button_container.pack(padx=20, pady=20)

        # 确认按钮（更大更明显）
        confirm_btn = ttk.Button(button_container, text="✅ 确认分割", command=self.on_confirm_click)
        confirm_btn.pack(pady=(0, 10), ipadx=20, ipady=5)

        # 取消按钮
        cancel_btn = ttk.Button(button_container, text="❌ 取消", command=self.on_cancel_click)
        cancel_btn.pack(ipadx=20, ipady=5)

        # 更新分割点列表
        self.update_split_list()
    
    def load_and_display_image(self):
        """加载并显示图像"""
        try:
            self.image = Image.open(self.image_path)
            
            # 计算缩放比例
            img_width, img_height = self.image.size
            scale_x = self.canvas_width / img_width
            scale_y = self.canvas_height / img_height
            self.scale_factor = min(scale_x, scale_y, 1.0)  # 不放大
            
            # 缩放图像
            new_width = int(img_width * self.scale_factor)
            new_height = int(img_height * self.scale_factor)
            
            display_image = self.image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            self.photo = ImageTk.PhotoImage(display_image)
            
            # 显示图像
            self.canvas_image = self.canvas.create_image(0, 0, anchor="nw", image=self.photo)
            
            # 更新滚动区域
            self.canvas.configure(scrollregion=(0, 0, new_width, new_height))
            
            # 绘制分割线
            self.draw_split_lines()
            
        except Exception as e:
            messagebox.showerror("错误", f"无法加载图像: {e}")
    
    def draw_split_lines(self):
        """绘制分割线"""
        # 清除现有分割线
        for line in self.split_lines:
            self.canvas.delete(line)
        self.split_lines.clear()
        
        # 绘制新的分割线
        img_width, img_height = self.image.size
        canvas_width = int(img_width * self.scale_factor)
        
        for split_point in self.split_points:
            y_pos = int(split_point.y_position * self.scale_factor)
            
            # 绘制分割线
            line_color = "red" if split_point.is_manual else "blue"
            line = self.canvas.create_line(0, y_pos, canvas_width, y_pos, 
                                         fill=line_color, width=2, tags="split_line")
            self.split_lines.append(line)
            
            # 添加标签
            label = self.canvas.create_text(10, y_pos - 10, text=f"分割点 {split_point.y_position}px", 
                                          fill=line_color, anchor="nw", tags="split_label")
            self.split_lines.append(label)
    
    def update_split_list(self):
        """更新分割点列表"""
        self.split_listbox.delete(0, tk.END)
        
        for i, split_point in enumerate(self.split_points):
            point_type = "手动" if split_point.is_manual else "自动"
            self.split_listbox.insert(tk.END, f"{i+1}. Y={split_point.y_position}px ({point_type})")
    
    def on_canvas_click(self, event):
        """画布点击事件"""
        # 检查是否点击在分割线上
        clicked_item = self.canvas.find_closest(event.x, event.y)[0]
        
        if "split_line" in self.canvas.gettags(clicked_item):
            # 开始拖拽分割线
            self.dragging_line = clicked_item
            self.drag_start_y = event.y
            self.canvas.configure(cursor="sb_v_double_arrow")
    
    def on_canvas_drag(self, event):
        """画布拖拽事件"""
        if self.dragging_line:
            # 计算新的Y位置
            dy = event.y - self.drag_start_y
            
            # 移动分割线
            self.canvas.move(self.dragging_line, 0, dy)
            
            # 更新对应的分割点
            line_index = self.split_lines.index(self.dragging_line) // 2  # 每个分割点有线和标签两个元素
            if 0 <= line_index < len(self.split_points):
                new_y = int((event.y) / self.scale_factor)
                self.split_points[line_index].y_position = max(0, min(new_y, self.image.size[1]))
                self.split_points[line_index].is_manual = True
            
            self.drag_start_y = event.y
    
    def on_canvas_release(self, event):
        """画布释放事件"""
        if self.dragging_line:
            self.dragging_line = None
            self.canvas.configure(cursor="")
            
            # 重新绘制分割线和更新列表
            self.draw_split_lines()
            self.update_split_list()
    
    def on_canvas_motion(self, event):
        """画布鼠标移动事件"""
        if not self.dragging_line:
            # 检查鼠标是否在分割线上
            item = self.canvas.find_closest(event.x, event.y)[0]
            if "split_line" in self.canvas.gettags(item):
                self.canvas.configure(cursor="sb_v_double_arrow")
            else:
                self.canvas.configure(cursor="")
    
    def add_split_point(self):
        """添加分割点"""
        # 简单实现：在图像中间添加一个分割点
        img_height = self.image.size[1]
        new_y = img_height // 2
        
        # 检查是否已存在相近的分割点
        for split_point in self.split_points:
            if abs(split_point.y_position - new_y) < 50:
                new_y += 100
                break
        
        new_split_point = SplitPoint(y_position=new_y, is_manual=True)
        self.split_points.append(new_split_point)
        self.split_points.sort(key=lambda x: x.y_position)
        
        self.draw_split_lines()
        self.update_split_list()
    
    def remove_split_point(self):
        """删除选中的分割点"""
        selection = self.split_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.split_points):
                del self.split_points[index]
                self.draw_split_lines()
                self.update_split_list()
    
    def reset_split_points(self):
        """重置分割点"""
        if messagebox.askyesno("确认", "确定要重置所有分割点吗？"):
            # 重新计算自动分割点
            from modules.image_splitter import ImageSplitter
            splitter = ImageSplitter()
            self.split_points = splitter.calculate_split_points(self.image.size[1])
            
            self.draw_split_lines()
            self.update_split_list()
    
    def on_confirm_click(self):
        """确认按钮点击"""
        if self.on_confirm:
            self.on_confirm(self.split_points)
        self.dialog.destroy()
    
    def on_cancel_click(self):
        """取消按钮点击"""
        if self.on_cancel:
            self.on_cancel()
        self.dialog.destroy()
    
    def show(self):
        """显示对话框"""
        self.dialog.wait_window()


def show_split_preview(parent, image_path: str, split_points: List[SplitPoint], 
                      on_confirm: Callable = None, on_cancel: Callable = None):
    """显示分割预览对话框"""
    dialog = SplitPreviewDialog(parent, image_path, split_points, on_confirm, on_cancel)
    dialog.show()
    return dialog
