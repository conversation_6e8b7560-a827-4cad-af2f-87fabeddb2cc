#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理模块
负责文件的添加、删除、预览等操作
"""

import os
import tempfile
from datetime import datetime
from typing import List, Optional, Callable
from PIL import Image, ImageTk, ImageGrab
import tkinter as tk

class FileManager:
    """文件管理器"""
    
    def __init__(self, log_callback: Optional[Callable] = None):
        self.selected_files: List[str] = []
        self.clipboard_images: List[str] = []
        self.log_callback = log_callback or self._default_log
        self.parsed_data: List[dict] = []  # 存储解析后的数据
        self.ai_responses: List[str] = []  # 存储AI原始响应
    
    def _default_log(self, message: str):
        """默认日志输出"""
        print(f"[FileManager] {message}")
    
    def log(self, message: str):
        """记录日志"""
        self.log_callback(message)
    
    def add_files(self, file_paths: List[str]) -> bool:
        """添加文件"""
        try:
            valid_files = []
            for file_path in file_paths:
                if os.path.exists(file_path):
                    valid_files.append(file_path)
                else:
                    self.log(f"文件不存在: {file_path}")
            
            if valid_files:
                self.selected_files.extend(valid_files)
                self.log(f"已添加 {len(valid_files)} 个文件")
                return True
            return False
        except Exception as e:
            self.log(f"添加文件失败: {str(e)}")
            return False
    
    def paste_image_from_clipboard(self) -> bool:
        """从剪贴板粘贴图像"""
        try:
            # 尝试从剪贴板获取图像
            image = ImageGrab.grabclipboard()
            
            if image:
                # 保存图像到临时文件
                temp_file = self._save_clipboard_image(image)
                if temp_file:
                    self.selected_files.append(temp_file)
                    self.clipboard_images.append(temp_file)
                    self.log(f"已粘贴图像: {os.path.basename(temp_file)}")
                    return True
                else:
                    self.log("保存剪贴板图像失败")
                    return False
            else:
                self.log("剪贴板中没有图像")
                return False
                
        except ImportError:
            self.log("缺少PIL.ImageGrab模块")
            return False
        except Exception as e:
            self.log(f"粘贴图像失败: {str(e)}")
            return False
    
    def _save_clipboard_image(self, image) -> Optional[str]:
        """保存剪贴板图像到临时文件"""
        try:
            # 创建临时文件
            temp_dir = tempfile.gettempdir()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"clipboard_image_{timestamp}.png"
            temp_path = os.path.join(temp_dir, temp_filename)
            
            # 保存图像
            image.save(temp_path, 'PNG')
            return temp_path
            
        except Exception as e:
            self.log(f"保存图像失败: {str(e)}")
            return None
    
    def remove_file(self, index: int) -> bool:
        """删除指定索引的文件"""
        try:
            if 0 <= index < len(self.selected_files):
                file_path = self.selected_files[index]
                
                # 如果是临时文件，删除它
                if file_path in self.clipboard_images:
                    try:
                        if os.path.exists(file_path):
                            os.remove(file_path)
                        self.clipboard_images.remove(file_path)
                    except Exception as e:
                        self.log(f"删除临时文件失败: {str(e)}")
                
                # 从列表中移除
                self.selected_files.pop(index)
                
                # 同时移除对应的解析数据
                if index < len(self.parsed_data):
                    self.parsed_data.pop(index)
                
                self.log(f"已删除文件: {os.path.basename(file_path)}")
                return True
            return False
        except Exception as e:
            self.log(f"删除文件失败: {str(e)}")
            return False
    
    def clear_all_files(self) -> bool:
        """清空所有文件"""
        try:
            # 清理临时图像文件
            for temp_file in self.clipboard_images:
                try:
                    if os.path.exists(temp_file):
                        os.remove(temp_file)
                except Exception as e:
                    self.log(f"删除临时文件失败: {str(e)}")
            
            self.selected_files.clear()
            self.clipboard_images.clear()
            self.parsed_data.clear()
            self.log("已清空所有文件")
            return True
        except Exception as e:
            self.log(f"清空文件失败: {str(e)}")
            return False
    
    def get_file_list(self) -> List[str]:
        """获取文件列表"""
        return self.selected_files.copy()
    
    def get_file_count(self) -> int:
        """获取文件数量"""
        return len(self.selected_files)
    
    def is_clipboard_image(self, file_path: str) -> bool:
        """判断是否为剪贴板图像"""
        return file_path in self.clipboard_images
    
    def get_file_info(self, file_path: str) -> dict:
        """获取文件信息"""
        try:
            info = {
                "path": file_path,
                "name": os.path.basename(file_path),
                "exists": os.path.exists(file_path),
                "is_clipboard": self.is_clipboard_image(file_path),
                "size": None,
                "image_size": None
            }
            
            if info["exists"]:
                info["size"] = os.path.getsize(file_path)
                
                # 如果是图像文件，获取尺寸
                if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp', '.tiff')):
                    try:
                        with Image.open(file_path) as img:
                            info["image_size"] = img.size
                    except Exception:
                        pass
            
            return info
        except Exception as e:
            self.log(f"获取文件信息失败: {str(e)}")
            return {"path": file_path, "name": os.path.basename(file_path), "error": str(e)}
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.clipboard_images:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
            except Exception:
                pass  # 忽略清理错误
    
    def set_parsed_data(self, index: int, data: dict):
        """设置解析后的数据"""
        # 确保parsed_data列表足够长
        while len(self.parsed_data) <= index:
            self.parsed_data.append(None)

        self.parsed_data[index] = data

    def get_parsed_data(self, index: int) -> Optional[dict]:
        """获取解析后的数据"""
        if 0 <= index < len(self.parsed_data):
            return self.parsed_data[index]
        return None

    def set_ai_response(self, index: int, response: str):
        """设置AI原始响应"""
        # 确保列表足够长
        while len(self.ai_responses) <= index:
            self.ai_responses.append("")
        self.ai_responses[index] = response

    def get_ai_response(self, index: int) -> str:
        """获取AI原始响应"""
        if 0 <= index < len(self.ai_responses):
            return self.ai_responses[index]
        return ""
    
    def get_all_parsed_data(self) -> List[dict]:
        """获取所有解析后的数据"""
        return [data for data in self.parsed_data if data is not None]
    
    def has_parsed_data(self) -> bool:
        """检查是否有解析后的数据"""
        return any(data is not None for data in self.parsed_data)

    def add_prefix_to_items(self, file_index: int, prefix: str) -> bool:
        """给指定文件的所有款号添加前缀"""
        try:
            if file_index >= len(self.parsed_data):
                return False

            parsed_data = self.parsed_data[file_index]
            if not parsed_data:
                return False

            items = parsed_data.get('items', [])
            modified = False

            for item in items:
                current_code = item.get('款号', '')
                if current_code and not current_code.startswith(f"{prefix}-"):
                    # 移除现有前缀（如果有的话）
                    if '-' in current_code:
                        current_code = current_code.split('-', 1)[-1]

                    # 添加新前缀
                    item['款号'] = f"{prefix}-{current_code}"
                    modified = True

            if modified:
                self.log(f"已给文件 {file_index} 的款号添加前缀: {prefix}")

            return modified

        except Exception as e:
            self.log(f"添加前缀失败: {str(e)}")
            return False

    def remove_prefix_from_items(self, file_index: int) -> bool:
        """移除指定文件的所有款号前缀"""
        try:
            if file_index >= len(self.parsed_data):
                return False

            parsed_data = self.parsed_data[file_index]
            if not parsed_data:
                return False

            items = parsed_data.get('items', [])
            modified = False

            for item in items:
                current_code = item.get('款号', '')
                if current_code and '-' in current_code:
                    # 移除前缀，保留后面的部分
                    item['款号'] = current_code.split('-', 1)[-1]
                    modified = True

            if modified:
                self.log(f"已移除文件 {file_index} 的款号前缀")

            return modified

        except Exception as e:
            self.log(f"移除前缀失败: {str(e)}")
            return False

    def add_prefix_to_all_items(self, prefix: str) -> int:
        """给所有文件的款号添加前缀"""
        modified_count = 0

        for i in range(len(self.parsed_data)):
            if self.add_prefix_to_items(i, prefix):
                modified_count += 1

        self.log(f"已给 {modified_count} 个文件的款号添加前缀: {prefix}")
        return modified_count

    def remove_prefix_from_all_items(self) -> int:
        """移除所有文件的款号前缀"""
        modified_count = 0

        for i in range(len(self.parsed_data)):
            if self.remove_prefix_from_items(i):
                modified_count += 1

        self.log(f"已移除 {modified_count} 个文件的款号前缀")
        return modified_count

    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    self.log(f"已删除临时文件: {temp_file}")
            except Exception as e:
                self.log(f"删除临时文件失败: {temp_file} - {str(e)}")

        self.temp_files.clear()
