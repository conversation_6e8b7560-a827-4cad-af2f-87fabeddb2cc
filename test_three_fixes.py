#!/usr/bin/env python3
"""
测试三个关键修复和改进
1. GUI布局重构为3列垂直布局
2. 修复分割图像命名逻辑
3. 修复AI处理逻辑和错误
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_three_key_fixes():
    """测试三个关键修复"""
    print("🔧 测试三个关键修复和改进")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        # 导入主界面
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI
        
        window = ModernTicketProcessorGUI()
        
        print("✅ 应用程序创建成功")
        
        # 测试1: GUI布局重构为3列
        print("\n📋 测试1: GUI布局重构为3列")
        layout_methods = [
            'create_column1_image_panel',
            'create_column2_results_panel', 
            'create_column3_logs_panel'
        ]
        
        for method in layout_methods:
            if hasattr(window, method):
                print(f"✅ 布局方法 {method} 存在")
            else:
                print(f"❌ 布局方法 {method} 缺失")
                
        # 测试2: 分割图像命名逻辑
        print("\n📋 测试2: 分割图像命名逻辑")
        naming_methods = [
            'get_display_name_for_file',
            'split_single_image'
        ]
        
        for method in naming_methods:
            if hasattr(window, method):
                print(f"✅ 命名方法 {method} 存在")
            else:
                print(f"❌ 命名方法 {method} 缺失")
                
        # 测试3: AI处理逻辑
        print("\n📋 测试3: AI处理逻辑")
        ai_methods = [
            'get_files_for_ai_processing',
            'start_ai_processing'
        ]
        
        for method in ai_methods:
            if hasattr(window, method):
                print(f"✅ AI方法 {method} 存在")
            else:
                print(f"❌ AI方法 {method} 缺失")
                
        # 测试AIProcessor的configure方法
        try:
            from modules.ai_processor import AIProcessor
            processor = AIProcessor()
            if hasattr(processor, 'configure'):
                print("✅ AIProcessor.configure 方法存在")
            else:
                print("❌ AIProcessor.configure 方法缺失")
        except Exception as e:
            print(f"❌ AIProcessor测试失败: {e}")
            
        # 显示窗口
        window.show()
        
        print("\n🎉 所有测试完成！")
        print("\n📋 手动验证建议:")
        print("1. 检查界面是否为3列布局")
        print("2. 上传图像并重命名为'GD340'")
        print("3. 分割图像，检查命名格式是否为'GD340_1.jpg'")
        print("4. 测试AI处理是否只处理分割后的图像")
        print("5. 验证AI处理不会出现'configure'错误")
        
        print("\n🎯 预期效果:")
        print("- 第1列：图像预览和管理（2列缩略图）")
        print("- 第2列：AI解析结果表格")
        print("- 第3列：系统日志和AI响应")
        print("- 分割命名：GD340_1.jpg, GD340_2.jpg, GD340_3.jpg")
        print("- AI处理：只处理3个分割文件，不处理原始文件")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(test_three_key_fixes())
