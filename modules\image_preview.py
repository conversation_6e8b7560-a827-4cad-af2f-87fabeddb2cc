#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像预览窗口模块
"""

import tkinter as tk
from tkinter import ttk
from PIL import Image, ImageTk
import os


class ImagePreviewWindow:
    """图像预览窗口"""
    
    def __init__(self, parent, image_path: str):
        self.parent = parent
        self.image_path = image_path
        self.original_image = None
        self.current_image = None
        self.photo = None
        self.scale_factor = 1.0
        self.min_scale = 0.1
        self.max_scale = 5.0
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.canvas_start_x = 0
        self.canvas_start_y = 0
        
        self.create_window()
        self.load_image()
        
    def create_window(self):
        """创建预览窗口"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(f"图像预览 - {os.path.basename(self.image_path)}")
        self.window.geometry("800x600")
        self.window.transient(self.parent)
        
        # 居中显示
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建画布
        self.create_canvas()
        
        # 绑定事件
        self.bind_events()
        
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = ttk.Frame(self.window)
        toolbar.pack(fill=tk.X, padx=5, pady=5)
        
        # 缩放按钮
        ttk.Button(toolbar, text="适合窗口", command=self.fit_to_window).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="实际大小", command=self.actual_size).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="放大", command=self.zoom_in).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(toolbar, text="缩小", command=self.zoom_out).pack(side=tk.LEFT, padx=(0, 5))
        
        # 缩放比例显示
        self.scale_label = ttk.Label(toolbar, text="100%")
        self.scale_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 关闭按钮
        ttk.Button(toolbar, text="关闭", command=self.window.destroy).pack(side=tk.RIGHT)
        
    def create_canvas(self):
        """创建画布"""
        # 创建框架
        canvas_frame = ttk.Frame(self.window)
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=(0, 5))
        
        # 创建画布和滚动条
        self.canvas = tk.Canvas(canvas_frame, bg="white")
        
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.canvas.xview)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        
        self.canvas.configure(xscrollcommand=h_scrollbar.set, yscrollcommand=v_scrollbar.set)
        
        # 布局
        self.canvas.grid(row=0, column=0, sticky="nsew")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
    def bind_events(self):
        """绑定事件"""
        # 鼠标滚轮缩放
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.canvas.bind("<Button-4>", self.on_mousewheel)  # Linux
        self.canvas.bind("<Button-5>", self.on_mousewheel)  # Linux
        
        # 鼠标拖拽
        self.canvas.bind("<Button-1>", self.on_drag_start)
        self.canvas.bind("<B1-Motion>", self.on_drag_motion)
        
        # 键盘快捷键
        self.window.bind("<Key>", self.on_key_press)
        self.window.focus_set()
        
        # 窗口大小改变
        self.window.bind("<Configure>", self.on_window_resize)
        
    def load_image(self):
        """加载图像"""
        try:
            self.original_image = Image.open(self.image_path)
            self.fit_to_window()
        except Exception as e:
            # 简单的错误处理，不使用messagebox
            print(f"无法加载图像: {str(e)}")
            self.window.destroy()
            
    def update_image_display(self):
        """更新图像显示"""
        if not self.original_image:
            return
            
        # 计算新尺寸
        width = int(self.original_image.width * self.scale_factor)
        height = int(self.original_image.height * self.scale_factor)
        
        # 缩放图像
        if self.scale_factor == 1.0:
            self.current_image = self.original_image
        else:
            self.current_image = self.original_image.resize((width, height), Image.Resampling.LANCZOS)
        
        # 转换为PhotoImage
        self.photo = ImageTk.PhotoImage(self.current_image)
        
        # 清空画布
        self.canvas.delete("all")
        
        # 显示图像
        self.canvas.create_image(width//2, height//2, image=self.photo)
        
        # 更新滚动区域
        self.canvas.configure(scrollregion=(0, 0, width, height))
        
        # 更新缩放比例显示
        self.scale_label.config(text=f"{int(self.scale_factor * 100)}%")
        
    def fit_to_window(self):
        """适合窗口大小"""
        if not self.original_image:
            return
            
        # 获取画布大小
        self.canvas.update_idletasks()
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            # 窗口还没有完全初始化，使用默认大小
            canvas_width = 750
            canvas_height = 500
        
        # 计算缩放比例
        scale_x = canvas_width / self.original_image.width
        scale_y = canvas_height / self.original_image.height
        self.scale_factor = min(scale_x, scale_y, 1.0)  # 不超过原始大小
        
        self.update_image_display()
        
    def actual_size(self):
        """实际大小"""
        self.scale_factor = 1.0
        self.update_image_display()
        
    def zoom_in(self):
        """放大"""
        new_scale = self.scale_factor * 1.2
        if new_scale <= self.max_scale:
            self.scale_factor = new_scale
            self.update_image_display()
            
    def zoom_out(self):
        """缩小"""
        new_scale = self.scale_factor / 1.2
        if new_scale >= self.min_scale:
            self.scale_factor = new_scale
            self.update_image_display()
            
    def on_mousewheel(self, event):
        """鼠标滚轮事件"""
        # 获取鼠标位置
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        # 确定缩放方向
        if event.delta > 0 or event.num == 4:
            # 向上滚动，放大
            self.zoom_in()
        else:
            # 向下滚动，缩小
            self.zoom_out()
            
    def on_drag_start(self, event):
        """开始拖拽"""
        self.drag_start_x = event.x
        self.drag_start_y = event.y
        self.canvas_start_x = self.canvas.canvasx(0)
        self.canvas_start_y = self.canvas.canvasy(0)
        
    def on_drag_motion(self, event):
        """拖拽移动"""
        # 计算移动距离
        dx = self.drag_start_x - event.x
        dy = self.drag_start_y - event.y
        
        # 移动画布视图
        self.canvas.xview_moveto((self.canvas_start_x + dx) / self.canvas.winfo_width())
        self.canvas.yview_moveto((self.canvas_start_y + dy) / self.canvas.winfo_height())
        
    def on_key_press(self, event):
        """键盘按键事件"""
        if event.keysym == "Escape":
            self.window.destroy()
        elif event.keysym == "plus" or event.keysym == "equal":
            self.zoom_in()
        elif event.keysym == "minus":
            self.zoom_out()
        elif event.keysym == "0":
            self.actual_size()
        elif event.keysym == "f" or event.keysym == "F":
            self.fit_to_window()
            
    def on_window_resize(self, event):
        """窗口大小改变事件"""
        # 只处理窗口本身的大小改变事件
        if event.widget == self.window:
            # 延迟更新，避免频繁调用
            self.window.after(100, self.fit_to_window)
