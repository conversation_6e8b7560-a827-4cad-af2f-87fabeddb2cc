#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
供应商管理模块
"""

import json
import os
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from datetime import datetime


@dataclass
class SupplierInfo:
    """供应商信息"""
    code: str
    name: str
    description: str = ""
    created_time: str = ""
    last_used: str = ""
    usage_count: int = 0
    is_active: bool = True


@dataclass
class SupplierGroup:
    """供应商分组数据"""
    supplier_code: str
    supplier_name: str
    total_items: int = 0
    total_files: int = 0
    last_updated: str = ""
    items: List[Dict] = None
    
    def __post_init__(self):
        if self.items is None:
            self.items = []


class SupplierManager:
    """供应商管理器"""
    
    def __init__(self, config_file: str = "suppliers.json"):
        self.config_file = config_file
        self.suppliers = self.load_suppliers()
        self.init_default_suppliers()
    
    def load_suppliers(self) -> Dict[str, SupplierInfo]:
        """加载供应商配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    suppliers = {}
                    for code, info in data.items():
                        suppliers[code] = SupplierInfo(**info)
                    return suppliers
            except Exception as e:
                print(f"加载供应商配置失败: {e}")
        return {}
    
    def save_suppliers(self):
        """保存供应商配置"""
        try:
            data = {}
            for code, supplier in self.suppliers.items():
                data[code] = asdict(supplier)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存供应商配置失败: {e}")
    
    def init_default_suppliers(self):
        """初始化默认供应商"""
        default_suppliers = [
            ("GT408", "供应商GT408"),
            ("GT251", "供应商GT251"),
            ("GT155", "供应商GT155"),
            ("GT253", "供应商GT253"),
            ("DSD258", "供应商DSD258"),
            ("DSD129", "供应商DSD129"),
            ("DSD106", "供应商DSD106"),
            ("GD340", "供应商GD340"),
            ("GT158", "供应商GT158"),
        ]
        
        for code, name in default_suppliers:
            if code not in self.suppliers:
                self.add_supplier(code, name, "默认供应商")
    
    def add_supplier(self, code: str, name: str, description: str = "") -> bool:
        """添加供应商"""
        if code in self.suppliers:
            return False
            
        supplier = SupplierInfo(
            code=code,
            name=name,
            description=description,
            created_time=datetime.now().isoformat(),
            last_used="",
            usage_count=0,
            is_active=True
        )
        
        self.suppliers[code] = supplier
        self.save_suppliers()
        return True
    
    def update_supplier(self, code: str, name: str = None, description: str = None, 
                       is_active: bool = None) -> bool:
        """更新供应商信息"""
        if code not in self.suppliers:
            return False
            
        supplier = self.suppliers[code]
        if name is not None:
            supplier.name = name
        if description is not None:
            supplier.description = description
        if is_active is not None:
            supplier.is_active = is_active
            
        self.save_suppliers()
        return True
    
    def delete_supplier(self, code: str) -> bool:
        """删除供应商"""
        if code in self.suppliers:
            del self.suppliers[code]
            self.save_suppliers()
            return True
        return False
    
    def get_supplier(self, code: str) -> Optional[SupplierInfo]:
        """获取供应商信息"""
        return self.suppliers.get(code)
    
    def get_all_suppliers(self, active_only: bool = True) -> List[SupplierInfo]:
        """获取所有供应商"""
        suppliers = list(self.suppliers.values())
        if active_only:
            suppliers = [s for s in suppliers if s.is_active]
        return sorted(suppliers, key=lambda x: x.usage_count, reverse=True)
    
    def get_supplier_codes(self, active_only: bool = True) -> List[str]:
        """获取供应商代码列表"""
        suppliers = self.get_all_suppliers(active_only)
        return [s.code for s in suppliers]
    
    def record_usage(self, code: str):
        """记录供应商使用"""
        if code in self.suppliers:
            supplier = self.suppliers[code]
            supplier.usage_count += 1
            supplier.last_used = datetime.now().isoformat()
            self.save_suppliers()
    
    def search_suppliers(self, keyword: str) -> List[SupplierInfo]:
        """搜索供应商"""
        keyword = keyword.lower()
        results = []
        
        for supplier in self.suppliers.values():
            if (keyword in supplier.code.lower() or 
                keyword in supplier.name.lower() or 
                keyword in supplier.description.lower()):
                results.append(supplier)
                
        return sorted(results, key=lambda x: x.usage_count, reverse=True)


class SupplierGroupManager:
    """供应商分组管理器"""
    
    def __init__(self, supplier_manager: SupplierManager):
        self.supplier_manager = supplier_manager
        self.groups: Dict[str, SupplierGroup] = {}
    
    def create_group(self, supplier_code: str) -> SupplierGroup:
        """创建供应商分组"""
        supplier = self.supplier_manager.get_supplier(supplier_code)
        supplier_name = supplier.name if supplier else supplier_code
        
        group = SupplierGroup(
            supplier_code=supplier_code,
            supplier_name=supplier_name,
            last_updated=datetime.now().isoformat()
        )
        
        self.groups[supplier_code] = group
        return group
    
    def get_group(self, supplier_code: str) -> Optional[SupplierGroup]:
        """获取供应商分组"""
        return self.groups.get(supplier_code)
    
    def get_or_create_group(self, supplier_code: str) -> SupplierGroup:
        """获取或创建供应商分组"""
        group = self.get_group(supplier_code)
        if group is None:
            group = self.create_group(supplier_code)
        return group
    
    def add_items_to_group(self, supplier_code: str, items: List[Dict], 
                          file_path: str = ""):
        """向分组添加商品"""
        group = self.get_or_create_group(supplier_code)
        
        # 添加商品
        for item in items:
            # 添加来源文件信息
            item['source_file'] = file_path
            item['added_time'] = datetime.now().isoformat()
            group.items.append(item)
        
        # 更新统计信息
        group.total_items = len(group.items)
        if file_path:
            # 统计文件数量
            source_files = set(item.get('source_file', '') for item in group.items)
            group.total_files = len([f for f in source_files if f])
        
        group.last_updated = datetime.now().isoformat()
        
        # 记录供应商使用
        self.supplier_manager.record_usage(supplier_code)
    
    def merge_duplicate_items(self, supplier_code: str):
        """合并重复商品"""
        group = self.get_group(supplier_code)
        if not group:
            return
        
        # 按商品名称和价格分组
        item_groups = {}
        for item in group.items:
            key = (item.get('name', ''), item.get('price', 0))
            if key not in item_groups:
                item_groups[key] = []
            item_groups[key].append(item)
        
        # 合并重复项
        merged_items = []
        for items in item_groups.values():
            if len(items) == 1:
                merged_items.append(items[0])
            else:
                # 合并数量
                merged_item = items[0].copy()
                total_quantity = sum(item.get('quantity', 0) for item in items)
                merged_item['quantity'] = total_quantity
                merged_item['merged_from'] = len(items)
                merged_items.append(merged_item)
        
        group.items = merged_items
        group.total_items = len(merged_items)
        group.last_updated = datetime.now().isoformat()
    
    def get_all_groups(self) -> List[SupplierGroup]:
        """获取所有分组"""
        return list(self.groups.values())
    
    def get_group_summary(self) -> Dict:
        """获取分组汇总信息"""
        total_suppliers = len(self.groups)
        total_items = sum(group.total_items for group in self.groups.values())
        total_files = sum(group.total_files for group in self.groups.values())
        
        return {
            'total_suppliers': total_suppliers,
            'total_items': total_items,
            'total_files': total_files,
            'groups': [
                {
                    'supplier_code': group.supplier_code,
                    'supplier_name': group.supplier_name,
                    'total_items': group.total_items,
                    'total_files': group.total_files,
                    'last_updated': group.last_updated
                }
                for group in self.groups.values()
            ]
        }
    
    def clear_group(self, supplier_code: str):
        """清空分组"""
        if supplier_code in self.groups:
            del self.groups[supplier_code]
    
    def clear_all_groups(self):
        """清空所有分组"""
        self.groups.clear()
    
    def export_group_data(self, supplier_code: str) -> Optional[Dict]:
        """导出分组数据"""
        group = self.get_group(supplier_code)
        if group:
            return asdict(group)
        return None
    
    def validate_group_data(self, supplier_code: str) -> Dict:
        """验证分组数据完整性"""
        group = self.get_group(supplier_code)
        if not group:
            return {'valid': False, 'error': '分组不存在'}
        
        issues = []
        warnings = []
        
        # 检查商品数据完整性
        for i, item in enumerate(group.items):
            if not item.get('name'):
                issues.append(f"商品 {i+1} 缺少名称")
            if not item.get('price'):
                warnings.append(f"商品 {i+1} 缺少价格")
            if not item.get('quantity'):
                warnings.append(f"商品 {i+1} 缺少数量")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'total_items': len(group.items)
        }
