#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责配置的保存、加载和验证
"""

import json
import os
from typing import Dict, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "user_config.json"):
        self.config_file = config_file
        self.config = {}
        self.load_config()
    
    def load_config(self) -> Dict:
        """加载配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, "r", encoding="utf-8") as f:
                    self.config = json.load(f)
            else:
                self.config = self.get_default_config()
            return self.config
        except Exception as e:
            print(f"加载配置失败: {str(e)}")
            self.config = self.get_default_config()
            return self.config
    
    def save_config(self, config: Dict) -> bool:
        """保存配置"""
        try:
            self.config = config
            with open(self.config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置失败: {str(e)}")
            return False
    
    def get_default_config(self) -> Dict:
        """获取默认配置"""
        return {
            "feishu": {
                "app_id": "",
                "app_secret": "",
                "table_id": ""
            },
            "ai": {
                "api_key": "",
                "api_url": ""
            },
            "prompt": self.get_default_prompt()
        }
    
    def get_default_prompt(self) -> str:
        """获取默认提示词"""
        return """你是一名专业的中文服饰票据信息抽取助手。请分析以下票据图像，提取关键信息并转换为标准JSON格式。

**分析要求：**
1. 仔细识别票据中的所有文字信息
2. 提取以下字段：
   - supplier：供应商名称（通常在票据顶部或印章处）
   - date：日期（统一格式为YYYY-MM-DD）
   - type：票据类型（任一商品数量或金额为负数则为"退货"，否则为"销售"）
   - items：商品明细数组，每项包含：
     * 款号：商品编号/货号
     * 颜色规格：颜色、尺码等规格信息
     * 数量：商品数量（退货为负数）
     * 单价：单位价格
     * 小计：该行总金额（数量×单价）

**注意事项：**
- 自动纠正识别错误（如"状号"→"款号"）
- 合理推断缺失信息（根据上下文判断供应商、日期）
- 确保数值计算正确（小计=数量×单价）
- 严格按JSON格式输出，不包含其他文字

**重要：直接输出JSON，不要任何解释或描述文字，只要纯JSON格式：**

{
  "supplier": "供应商名称",
  "date": "YYYY-MM-DD",
  "type": "销售或退货",
  "items": [
    {
      "款号": "商品编号",
      "颜色规格": "颜色尺码信息",
      "数量": 数量,
      "单价": 单价,
      "小计": 小计金额
    }
  ]
}"""
    
    def validate_config(self, config: Dict) -> tuple[bool, str]:
        """验证配置是否完整"""
        feishu_config = config.get("feishu", {})
        ai_config = config.get("ai", {})
        
        if not feishu_config.get("app_id"):
            return False, "请填写飞书App ID"
        
        if not feishu_config.get("app_secret"):
            return False, "请填写飞书App Secret"
        
        if not feishu_config.get("table_id"):
            return False, "请填写电子表格ID"
        
        if not ai_config.get("api_key"):
            return False, "请填写AI API Key"
        
        if not ai_config.get("api_url"):
            return False, "请填写AI API URL"
        
        return True, "配置验证通过"
    
    def get_feishu_config(self) -> Dict:
        """获取飞书配置"""
        return self.config.get("feishu", {})
    
    def get_ai_config(self) -> Dict:
        """获取AI配置"""
        return self.config.get("ai", {})
    
    def get_prompt(self) -> str:
        """获取提示词"""
        return self.config.get("prompt", self.get_default_prompt())
    
    def update_prompt(self, prompt: str) -> bool:
        """更新提示词"""
        self.config["prompt"] = prompt
        return self.save_config(self.config)
