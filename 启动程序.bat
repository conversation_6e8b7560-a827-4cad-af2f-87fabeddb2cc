@echo off
chcp 65001 >nul
echo ========================================
echo    飞书票据处理系统
echo ========================================
echo.
echo 正在启动程序...
echo.

python main.py

if %errorlevel% neq 0 (
    echo.
    echo ❌ 程序启动失败！
    echo.
    echo 可能的解决方案：
    echo 1. 检查Python是否已安装
    echo 2. 安装依赖包：pip install -r requirements.txt
    echo 3. 检查文件是否完整
    echo.
    pause
) else (
    echo.
    echo ✅ 程序已正常退出
)
