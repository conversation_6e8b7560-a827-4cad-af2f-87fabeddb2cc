#!/usr/bin/env python3
"""
快速测试脚本 - 验证修复效果
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """快速测试主函数"""
    print("🔧 快速测试智能票据处理系统")
    print("=" * 50)
    
    try:
        # 设置高DPI支持
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        app = QApplication(sys.argv)
        
        print("✅ PyQt5应用创建成功")
        
        # 导入主界面
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI
        
        print("✅ 主界面模块导入成功")
        
        # 创建窗口
        window = ModernTicketProcessorGUI()
        
        print("✅ 主窗口创建成功")
        
        # 检查关键属性
        required_attrs = [
            'main_thumbnail_layout',
            'main_thumbnail_items', 
            'image_rename_cache',
            'selected_main_thumbnail'
        ]
        
        for attr in required_attrs:
            if hasattr(window, attr):
                print(f"✅ {attr} 属性存在")
            else:
                print(f"❌ {attr} 属性缺失")
                
        # 显示窗口
        window.show()
        
        print("\n🎉 测试通过！系统已启动")
        print("📋 请手动测试以下功能:")
        print("1. 上传图像文件")
        print("2. 悬停操作按钮")
        print("3. 图像重命名")
        print("4. 删除图像")
        print("5. 全屏预览")
        
        return app.exec_()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
