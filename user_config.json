{"ai": {"api_key": "e780825e-5759-4731-9d04-d17a687e1dac", "api_url": "https://ark.cn-beijing.volces.com/api/v3/chat/completions", "model_name": "doubao-1.5-vision-pro-250328", "temperature": 70, "max_tokens": 4000, "timeout": 60}, "feishu": {"app_id": "", "app_secret": "", "spreadsheet_id": ""}, "prompt": {"content": "你是一名专业的中文服饰票据信息抽取助手。请分析以下票据图像，提取所有关键信息并转换为标准JSON格式。\n\n**分析要求：**\n\n1. 仔细识别票据中的所有文字信息\n2. 提取以下字段：\n    - supplier：供应商名称（通常在票据顶部或印章处）\n    - date：日期（统一格式为YYYY-MM-DD）\n    - type：票据类型（任一商品数量或金额为负数则为\"退货\"，否则为\"销售\"，无论正负都需要全部识别后按照格式输出）\n    - items：商品明细数组，每项包含：\n        - 款号：商品编号/货号\n        - 颜色规格：颜色、尺码等规格信息\n        - 数量：商品数量（退货为负数）\n        - 单价：单位价格\n        - 小计：该行总金额（数量×单价）\n\n**注意事项：**\n\n- 自动纠正识别错误（如\"状号\"→\"款号\"）\n- 合理推断缺失信息（根据上下文判断供应商、日期）\n- 确保数值计算正确（小计=数量×单价）如有错误的在款号前面加上“*”标记\n- 严格按JSON格式输出，不包含其他文字\n\n**重要：直接输出JSON，不要任何解释或描述文字，只要纯JSON格式：**\n\n{\n\n\"supplier\": \"供应商名称\",\n\n\"date\": \"YYYY-MM-DD\",\n\n\"items\": [\n\n```\n{\n\n  \"款号\": \"商品编号\",\n\n  \"颜色规格\": \"颜色尺码信息\",\n\n  \"数量\": 数量,\n\n  \"单价\": 单价,\n \n  \"小计\": 小计金额\n\n}\n\n```\n\n]   \n\n}\n\n除此之外不要输出任何其他无关信息。"}, "ui": {"theme": "dark", "font_size": 10, "auto_save": true, "remember_window": true}}