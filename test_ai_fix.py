#!/usr/bin/env python3
"""
测试AI处理修复
验证configure方法和process_image方法是否正常工作
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_ai_processor_fix():
    """测试AI处理器修复"""
    print("🔧 测试AI处理器修复")
    print("=" * 50)
    
    try:
        # 测试AIProcessor
        from modules.ai_processor import AIProcessor
        
        processor = AIProcessor()
        print("✅ AIProcessor创建成功")
        
        # 测试configure方法
        test_config = {
            'api_key': 'test_key',
            'api_url': 'https://test.com/api',
            'model_name': 'test_model',
            'prompt_template': 'test prompt'
        }
        
        try:
            processor.configure(test_config)
            print("✅ configure方法调用成功")
            
            # 验证配置是否正确设置
            assert processor.api_key == 'test_key'
            assert processor.api_url == 'https://test.com/api'
            assert processor.model_name == 'test_model'
            assert processor.prompt_template == 'test prompt'
            print("✅ 配置参数设置正确")
            
        except Exception as e:
            print(f"❌ configure方法测试失败: {e}")
            return False
            
        # 测试process_image方法是否存在
        if hasattr(processor, 'process_image'):
            print("✅ process_image方法存在")
        else:
            print("❌ process_image方法缺失")
            return False
            
        # 测试AIProcessingThread
        print("\n📋 测试AIProcessingThread")
        from modules.pyqt5_main_gui import AIProcessingThread
        
        test_files = ['test1.jpg', 'test2.jpg']
        test_ai_config = {
            'api_key': 'test_key',
            'api_url': 'https://test.com/api',
            'model_name': 'test_model'
        }
        test_prompt = 'test prompt'
        
        try:
            thread = AIProcessingThread(test_files, test_ai_config, test_prompt)
            print("✅ AIProcessingThread创建成功")
            
            # 验证线程属性
            assert thread.files == test_files
            assert thread.ai_config == test_ai_config
            assert thread.prompt == test_prompt
            print("✅ 线程参数设置正确")
            
        except Exception as e:
            print(f"❌ AIProcessingThread测试失败: {e}")
            return False
            
        print("\n🎉 所有AI处理器测试通过！")
        
        print("\n📋 修复内容总结:")
        print("1. ✅ 添加了AIProcessor.configure(config_dict)方法")
        print("2. ✅ 添加了AIProcessor.process_image(image_path, prompt)方法")
        print("3. ✅ 修复了AIProcessingThread的参数传递")
        print("4. ✅ 统一了错误处理格式")
        
        print("\n🔍 预期效果:")
        print("- AI处理不再出现'configure'属性错误")
        print("- 支持豆包AI API的正确调用")
        print("- 错误信息更加详细和准确")
        print("- 处理结果格式统一")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_config_format():
    """测试AI配置格式"""
    print("\n🔧 测试AI配置格式")
    print("-" * 30)
    
    # 模拟豆包AI配置
    doubao_config = {
        'api_key': 'e780825e-5759-4731-9d04-d17a687e1dac',
        'api_url': 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
        'model_name': 'doubao-1-5-vision-pro-32k-250115',
        'prompt_template': '请分析这张票据图像...',
        'temperature': 0.7,
        'max_tokens': 4000,
        'timeout': 60
    }
    
    try:
        from modules.ai_processor import AIProcessor
        processor = AIProcessor()
        
        # 测试配置
        processor.configure(doubao_config)
        
        print("✅ 豆包AI配置测试成功")
        print(f"   API URL: {processor.api_url}")
        print(f"   模型名称: {processor.model_name}")
        print(f"   API类型: {processor.detect_api_type()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 豆包AI配置测试失败: {e}")
        return False

if __name__ == "__main__":
    success1 = test_ai_processor_fix()
    success2 = test_ai_config_format()
    
    if success1 and success2:
        print("\n🎊 所有测试通过！AI处理修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，请检查修复")
        sys.exit(1)
