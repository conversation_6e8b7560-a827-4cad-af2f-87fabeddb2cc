#!/usr/bin/env python3
"""
智能票据处理系统 - PyQt5版本启动文件
现代化界面设计，支持黑色主题和标签页布局
"""

import sys
import os

# 添加模块路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    missing_packages = []
    
    try:
        import PyQt5
    except ImportError:
        missing_packages.append("PyQt5")
        
    try:
        import PIL
    except ImportError:
        missing_packages.append("Pillow")
        
    try:
        import requests
    except ImportError:
        missing_packages.append("requests")
        
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
        
    return True

def main():
    """主函数"""
    print("🚀 启动智能票据处理系统 - PyQt5版本")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
        
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import Qt
        from modules.pyqt5_main_gui import ModernTicketProcessorGUI
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("智能票据处理系统")
        app.setApplicationVersion("2.0 PyQt5版")
        app.setOrganizationName("智能票据处理")
        
        # 设置高DPI支持
        app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
        app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
        
        print("✅ 依赖检查通过")
        print("🎨 应用黑色主题")
        print("📱 创建现代化界面")
        
        # 创建主窗口
        window = ModernTicketProcessorGUI()
        window.show()
        
        print("🎉 界面启动成功!")
        print("\n界面特性:")
        print("  • 🌙 深色主题设计")
        print("  • 📑 标签页布局")
        print("  • 📊 左右分栏显示")
        print("  • 🔧 现代化配置界面")
        print("  • 📈 实时进度显示")
        print("  • 🎯 优化的用户体验")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖包已正确安装")
        input("按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")

if __name__ == "__main__":
    main()
