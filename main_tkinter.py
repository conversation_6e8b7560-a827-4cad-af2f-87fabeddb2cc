#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书票据处理系统 - tkinter版本启动文件（备份版本）
模块化版本，支持AI解析和飞书上传分离
"""

import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        ('lark_oapi', '飞书API SDK'),
        ('requests', '网络请求库'),
        ('PIL', 'Python图像库'),
        ('tkinter', 'GUI界面库')
    ]
    
    missing_packages = []
    
    for package, description in required_packages:
        try:
            if package == 'PIL':
                import PIL
            elif package == 'tkinter':
                import tkinter
            else:
                __import__(package)
            print(f"✅ {description} 已安装")
        except ImportError:
            print(f"❌ {description} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包：{', '.join(missing_packages)}")
        print("请运行以下命令安装：")
        print("pip install -r requirements.txt")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 智能票据处理系统 - tkinter版本（备份）")
    print("=" * 60)
    print("✨ 功能特性:")
    print("- 🔧 模块化代码结构")
    print("- 🤖 AI解析和飞书上传分离")
    print("- 📋 支持剪贴板粘贴图像")
    print("- 🖼️ 实时图像预览")
    print("- ⚙️ 可视化配置管理")
    print("- 📊 详细的处理日志")
    print("- 🔪 智能图像分割功能")
    print("- 🏢 供应商分组管理")
    print("- 📑 多标签页结果显示")
    print("- 🎯 批量智能处理")
    print("=" * 60)
    
    # 检查依赖
    print("\n📦 检查依赖包...")
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    try:
        # 导入并启动tkinter GUI
        print("\n🚀 启动应用...")
        from modules.main_gui import MainGUI
        
        app = MainGUI()
        app.run()
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖包已正确安装")
        input("\n按回车键退出...")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")

if __name__ == "__main__":
    main()
